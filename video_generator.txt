import cv2
import numpy as np
from moviepy.editor import ImageClip, CompositeVideoClip
from PIL import Image, ImageEnhance
import os

class VisualPromptVideoGenerator:
    def __init__(self, image_path, output_path="output_video.mp4", duration=8):
        self.image_path = image_path
        self.output_path = output_path
        self.duration = duration

    def apply_effects(self, base_image):
        # Convert to PIL image for easier enhancement
        img = Image.fromarray(base_image)

        # Brightness/contrast enhancement
        img = ImageEnhance.Contrast(img).enhance(1.2)
        img = ImageEnhance.Color(img).enhance(1.15)

        # Convert back to OpenCV format
        image_np = np.array(img)

        # Add ambient particles effect (basic noise overlay)
        noise = np.random.randint(0, 50, image_np.shape, dtype='uint8')
        image_np = cv2.addWeighted(image_np, 0.98, noise, 0.02, 0)

        # Add soft light glare
        glare = np.zeros_like(image_np)
        cv2.circle(glare, (image_np.shape[1]//2, image_np.shape[0]//4), 180, (255, 255, 200), -1)
        image_np = cv2.addWeighted(image_np, 1.0, glare, 0.05, 0)

        return image_np

    def generate_video(self):
        if not os.path.exists(self.image_path):
            raise FileNotFoundError("Image file not found.")

        base_image = cv2.imread(self.image_path)
        image_with_fx = self.apply_effects(base_image)

        # Save temp image
        temp_image_path = "temp_with_effects.jpg"
        cv2.imwrite(temp_image_path, image_with_fx)

        # Load into MoviePy and add Ken Burns (zoom in)
        clip = ImageClip(temp_image_path).set_duration(self.duration).resize(width=1080)

        zoomed = clip.fx(vfx.zoom_in, 1.1)  # Subtle cinematic zoom

        final = CompositeVideoClip([zoomed])
        final.write_videofile(self.output_path, fps=24)

        # Cleanup
        os.remove(temp_image_path)
        print(f"🎞️ Video saved: {self.output_path}")
