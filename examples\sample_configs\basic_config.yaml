# Basic Configuration for Storytelling Pipeline
# This is a minimal configuration suitable for getting started

# Story Generation Settings
story_generation:
  default_method: "template"  # Use template method as default (no API key required)
  
  # Template-based generation settings
  template:
    genres: ["fantasy", "adventure", "mystery", "sci-fi"]
    settings: ["ancient castle", "mystical forest", "space station", "hidden temple"]
    characters: ["brave hero", "wise mentor", "mysterious stranger", "loyal companion"]
    conflicts: ["quest for truth", "battle against evil", "journey home", "search for treasure"]
  
  # Content validation
  validation:
    min_length: 50
    max_length: 1000
    content_filter: true

# Image Generation Settings
image_generation:
  primary_service: "openai"  # Requires OPENAI_API_KEY
  fallback_service: "stability"  # Requires STABILITY_API_KEY
  
  # OpenAI DALL-E settings
  openai:
    model: "dall-e-3"
    size: "1024x1024"
    quality: "standard"  # Use "hd" for higher quality (costs more)
    style: "vivid"
  
  # Prompt enhancement
  prompt_enhancement:
    style_keywords: ["cinematic", "detailed", "high quality"]
    lighting_keywords: ["dramatic lighting", "soft shadows"]
    quality_keywords: ["masterpiece", "professional"]

# Visual Effects Settings
effects_processing:
  default_intensity: "medium"
  
  # Available effects will be auto-selected based on story genre/mood
  # You can also specify effects manually in CLI commands
  
  output_formats:
    static: ["png", "jpg"]
    animated: ["gif", "mp4"]
    video_settings:
      fps: 24
      bitrate: "3M"

# Pipeline Settings
pipeline:
  output_structure:
    base_dir: "output"
    timestamp_format: "%Y%m%d_%H%M%S"
    subdirs: ["stories", "images", "processed"]
  
  logging:
    level: "INFO"
    file: "storytelling_pipeline.log"
  
  error_handling:
    retry_attempts: 3
    retry_delay: 2
    graceful_degradation: true
  
  performance:
    batch_size: 3  # Conservative batch size
    concurrent_requests: 2
    cache_enabled: true

# CLI Settings
cli:
  default_batch_size: 1
  progress_bar: true
  verbose: false
