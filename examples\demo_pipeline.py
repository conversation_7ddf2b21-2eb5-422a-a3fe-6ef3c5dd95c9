#!/usr/bin/env python3
"""
Demo <PERSON>ript for Storytelling Pipeline

This script demonstrates the complete functionality of the storytelling pipeline
with various generation methods and output formats.
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from core.pipeline import StorytellingPipeline
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()


async def demo_basic_generation():
    """Demonstrate basic story generation."""
    console.print(Panel("🎭 Demo 1: Basic Story Generation", style="blue"))
    
    pipeline = StorytellingPipeline()
    
    # Generate a single story using template method (no API key required)
    console.print("Generating story using template method...")
    
    result = await pipeline.generate_complete_story(
        story_method="template",
        effects=["dramatic", "vintage"],
        output_format="png"
    )
    
    if result["success"]:
        story = result["story_data"]
        console.print(f"✅ Generated story: [bold]{story['title']}[/bold]")
        console.print(f"📖 Genre: {story['genre']}")
        console.print(f"🎭 Mood: {story['mood']}")
        console.print(f"📁 Output: {result['final_output']}")
    else:
        console.print("❌ Generation failed:", result.get("errors"))
    
    return result


async def demo_batch_generation():
    """Demonstrate batch story generation."""
    console.print(Panel("🎬 Demo 2: Batch Story Generation", style="green"))
    
    pipeline = StorytellingPipeline()
    
    console.print("Generating 3 stories in batch...")
    
    result = await pipeline.generate_batch_stories(
        count=3,
        story_method="random",
        output_format="png"
    )
    
    if result["success"]:
        console.print(f"✅ Batch completed: {result['successful_generations']}/{result['total_requested']} successful")
        
        # Show story titles
        table = Table(title="Generated Stories")
        table.add_column("Index", style="cyan")
        table.add_column("Title", style="green")
        table.add_column("Genre", style="yellow")
        
        for i, story_result in enumerate(result["stories"]):
            if story_result.get("success"):
                story = story_result["story_data"]
                table.add_row(str(i+1), story["title"], story["genre"])
        
        console.print(table)
    else:
        console.print("❌ Batch generation failed")
    
    return result


async def demo_buddha_stories():
    """Demonstrate Buddha-themed story generation."""
    console.print(Panel("🧘 Demo 3: Buddha Story Generation", style="yellow"))
    
    pipeline = StorytellingPipeline()
    
    console.print("Generating Buddha-themed story...")
    
    result = await pipeline.generate_complete_story(
        story_method="buddha",
        effects=["golden_hour", "soft"],
        output_format="png"
    )
    
    if result["success"]:
        story = result["story_data"]
        console.print(f"✅ Generated Buddha story: [bold]{story['title']}[/bold]")
        console.print(f"📍 Setting: {story['setting']}")
        console.print(f"🎭 Mood: {story['mood']}")
        console.print(f"📖 Summary: {story['plot_summary'][:100]}...")
    else:
        console.print("❌ Buddha story generation failed")
    
    return result


async def demo_effects_showcase():
    """Demonstrate different visual effects."""
    console.print(Panel("✨ Demo 4: Visual Effects Showcase", style="magenta"))
    
    pipeline = StorytellingPipeline()
    
    # Generate base story
    console.print("Generating base story for effects demonstration...")
    
    base_result = await pipeline.generate_complete_story(
        story_method="template",
        effects=[],  # No effects initially
        output_format="png"
    )
    
    if not base_result["success"]:
        console.print("❌ Failed to generate base story")
        return
    
    # Apply different effects to the same story
    effects_to_test = [
        ["dramatic", "hdr"],
        ["vintage", "soft"],
        ["vibrant", "oil_painting"],
        ["watercolor", "dreamy"]
    ]
    
    console.print("Applying different effects to the same story...")
    
    for i, effects in enumerate(effects_to_test):
        console.print(f"  Applying effects: {', '.join(effects)}")
        
        # Note: In a real scenario, you'd apply effects to an existing image
        # For demo purposes, we're generating new stories with different effects
        effect_result = await pipeline.generate_complete_story(
            story_method="template",
            effects=effects,
            output_format="png"
        )
        
        if effect_result["success"]:
            console.print(f"    ✅ Effects applied: {effect_result['final_output']}")
        else:
            console.print(f"    ❌ Effects failed: {effects}")


def demo_pipeline_status():
    """Demonstrate pipeline status and validation."""
    console.print(Panel("📊 Demo 5: Pipeline Status & Validation", style="cyan"))
    
    pipeline = StorytellingPipeline()
    
    # Show pipeline status
    status = pipeline.get_pipeline_status()
    
    console.print("Pipeline Status:")
    console.print(f"  🤖 Initialized: {status['pipeline_initialized']}")
    console.print(f"  📁 Current Session: {status['current_session'] or 'None'}")
    console.print(f"  📈 Progress: {status['progress']['percentage']:.1f}%")
    
    # Show available components
    components = status['components']
    console.print("\nAvailable Components:")
    console.print(f"  📝 Story Methods: {len(components['story_generator']['available_methods'])}")
    console.print(f"  🎨 Image Services: {len(components['image_generator']['available_services'])}")
    console.print(f"  ✨ Effect Categories: {len(components['effects_processor']['available_effects'])}")
    
    # Validate setup
    validation = pipeline.validate_pipeline_setup()
    
    console.print(f"\nSetup Validation: {'✅ Valid' if validation['valid'] else '❌ Invalid'}")
    
    if validation['warnings']:
        console.print("⚠️  Warnings:")
        for warning in validation['warnings']:
            console.print(f"    • {warning}")
    
    if validation['recommendations']:
        console.print("💡 Recommendations:")
        for rec in validation['recommendations'][:3]:  # Show first 3
            console.print(f"    • {rec}")


async def demo_interactive_story():
    """Demonstrate interactive story creation."""
    console.print(Panel("🎮 Demo 6: Interactive Story Creation", style="red"))
    
    pipeline = StorytellingPipeline()
    
    console.print("This would normally prompt for user input...")
    console.print("For demo purposes, we'll simulate interactive choices:")
    
    # Simulate interactive choices
    interactive_params = {
        "genre": "fantasy",
        "setting": "enchanted forest",
        "characters": ["brave knight", "wise wizard"],
        "mood": "epic"
    }
    
    console.print(f"Simulated choices: {interactive_params}")
    
    # Generate with simulated interactive input
    result = await pipeline.generate_complete_story(
        story_method="template",  # Use template with custom params
        effects=["dramatic", "fantasy"],
        output_format="png",
        **interactive_params
    )
    
    if result["success"]:
        story = result["story_data"]
        console.print(f"✅ Interactive story created: [bold]{story['title']}[/bold]")
    else:
        console.print("❌ Interactive story creation failed")


async def main():
    """Run all demonstrations."""
    console.print(Panel("🎭 Storytelling Pipeline Demonstration", style="bold blue"))
    console.print("This demo showcases the complete functionality of the storytelling pipeline.\n")
    
    try:
        # Run demonstrations
        await demo_basic_generation()
        console.print()
        
        await demo_batch_generation()
        console.print()
        
        await demo_buddha_stories()
        console.print()
        
        await demo_effects_showcase()
        console.print()
        
        demo_pipeline_status()
        console.print()
        
        await demo_interactive_story()
        console.print()
        
        console.print(Panel("✅ All demonstrations completed successfully!", style="bold green"))
        console.print("\n📁 Check the 'output' directory for generated content.")
        console.print("🔧 Customize settings in 'config.yaml' for your needs.")
        console.print("🚀 Use 'python main_pipeline.py --help' for CLI options.")
        
    except Exception as e:
        console.print(f"\n💥 Demo failed with error: {str(e)}")
        console.print("This is likely due to missing API keys or dependencies.")
        console.print("The pipeline will work with template-based generation even without API keys.")


if __name__ == "__main__":
    asyncio.run(main())
