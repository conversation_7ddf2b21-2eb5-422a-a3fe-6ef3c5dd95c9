"""
Integration Pipeline & Orchestration

Main StorytellingPipeline class that orchestrates the complete workflow with error handling,
progress tracking, quality validation, and comprehensive logging.
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable
from pathlib import Path
import json

from config.settings import load_config, setup_logging
from utils.file_manager import FileManager
from .story_generator import StoryPromptGenerator
from .image_generator import ImageGenerator
from .effects_processor import EffectsProcessor

logger = logging.getLogger(__name__)


class StorytellingPipeline:
    """Main pipeline orchestrator for automated storytelling and visual content generation."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the storytelling pipeline.

        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        self.config = load_config(config_path)
        setup_logging(self.config)

        # Initialize components
        self.file_manager = FileManager(
            base_output_dir=self.config.get('pipeline.output_structure.base_dir', 'output')
        )

        self.story_generator = StoryPromptGenerator(self.config)
        self.image_generator = ImageGenerator(self.config, self.file_manager)
        self.effects_processor = EffectsProcessor(self.config, self.file_manager)

        # Pipeline state
        self.current_session = None
        self.progress_callback = None
        self.total_steps = 0
        self.completed_steps = 0

        logger.info("Storytelling pipeline initialized successfully")

    def set_progress_callback(self, callback: Callable[[int, int, str], None]):
        """Set callback function for progress updates.

        Args:
            callback: Function that receives (completed_steps, total_steps, current_task)
        """
        self.progress_callback = callback

    def _update_progress(self, task_description: str):
        """Update progress and call callback if set."""
        self.completed_steps += 1
        if self.progress_callback:
            self.progress_callback(self.completed_steps, self.total_steps, task_description)

        logger.info(f"Progress: {self.completed_steps}/{self.total_steps} - {task_description}")

    async def generate_complete_story(self,
                                    story_method: str = "auto",
                                    story_prompt: Optional[str] = None,
                                    image_service: str = "auto",
                                    effects: Optional[List[str]] = None,
                                    output_format: str = "png",
                                    **kwargs) -> Dict[str, Any]:
        """Generate complete story with image and effects.

        Args:
            story_method: Story generation method (openai, template, interactive, random, buddha, auto)
            story_prompt: Custom story prompt
            image_service: Image generation service (openai, stability, auto)
            effects: List of effects to apply
            output_format: Final output format (png, jpg, gif, mp4)
            **kwargs: Additional parameters

        Returns:
            Complete generation result
        """
        self.total_steps = 4  # Story, Image, Effects, Finalization
        self.completed_steps = 0

        # Create session directory
        session_dir = self.file_manager.create_session_directory()
        self.current_session = session_dir

        logger.info(f"Starting complete story generation in session: {session_dir}")

        result = {
            "success": False,
            "session_directory": str(session_dir),
            "timestamp": datetime.now().isoformat(),
            "story_data": None,
            "image_data": None,
            "effects_data": None,
            "final_output": None,
            "errors": []
        }

        try:
            # Step 1: Generate Story
            self._update_progress("Generating story prompt")
            story_result = await self.story_generator.generate_story(
                method=story_method,
                prompt=story_prompt,
                **kwargs
            )

            if not story_result:
                raise Exception("Story generation failed")

            result["story_data"] = story_result

            # Save story data
            story_file = self.file_manager.save_story_data(story_result)
            logger.info(f"Story saved: {story_file}")

            # Step 2: Generate Image
            self._update_progress("Generating image from story")

            # Create image prompt from story
            image_prompt = self.story_generator.generate_image_prompt(story_result)

            image_result = await self.image_generator.generate_image(
                prompt=image_prompt,
                story_data=story_result,
                service=image_service,
                **kwargs
            )

            if not image_result.get("success"):
                raise Exception(f"Image generation failed: {image_result.get('error')}")

            result["image_data"] = image_result

            # Step 3: Apply Effects
            self._update_progress("Applying visual effects")

            if effects is None:
                # Auto-select effects based on story mood/genre
                effects = self._auto_select_effects(story_result)

            effects_result = await self.effects_processor.process_image(
                image_path=image_result["saved_path"],
                effects=effects,
                output_format=output_format,
                **kwargs
            )

            if not effects_result.get("success"):
                raise Exception(f"Effects processing failed: {effects_result.get('error')}")

            result["effects_data"] = effects_result
            result["final_output"] = effects_result["output_path"]

            # Step 4: Finalization
            self._update_progress("Finalizing and creating summary")

            # Create session summary
            summary_data = {
                "story_method": story_method,
                "image_service": image_service,
                "effects_applied": effects,
                "output_format": output_format,
                "story_title": story_result.get("title", "Untitled"),
                "generation_time": time.time() - result.get("start_time", time.time()),
                "files_created": {
                    "story": str(story_file),
                    "image": image_result["saved_path"],
                    "final_output": effects_result["output_path"]
                }
            }

            summary_file = self.file_manager.create_summary_report(summary_data)
            result["summary_file"] = str(summary_file)

            result["success"] = True
            logger.info("Complete story generation finished successfully")

        except Exception as e:
            error_msg = f"Pipeline execution failed: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            result["success"] = False

        return result

    def _auto_select_effects(self, story_data: Dict[str, Any]) -> List[str]:
        """Auto-select effects based on story characteristics.

        Args:
            story_data: Story data dictionary

        Returns:
            List of recommended effects
        """
        effects = []

        genre = story_data.get("genre", "").lower()
        mood = story_data.get("mood", "").lower()

        # Genre-based effects
        if genre in ["fantasy", "spiritual"]:
            effects.extend(["dramatic", "hdr"])
        elif genre in ["sci-fi", "cyberpunk"]:
            effects.extend(["vibrant", "hdr"])
        elif genre in ["mystery", "horror"]:
            effects.extend(["dramatic", "vintage"])
        elif genre in ["romance", "peaceful"]:
            effects.extend(["soft", "watercolor"])
        else:
            effects.append("dramatic")  # Default

        # Mood-based effects
        if "dark" in mood or "mysterious" in mood:
            effects.append("vintage")
        elif "epic" in mood or "heroic" in mood:
            effects.append("hdr")
        elif "peaceful" in mood or "serene" in mood:
            effects.append("soft")
        elif "vibrant" in mood or "energetic" in mood:
            effects.append("vibrant")

        # Remove duplicates while preserving order
        return list(dict.fromkeys(effects))

    async def generate_batch_stories(self, count: int,
                                   story_method: str = "random",
                                   image_service: str = "auto",
                                   effects: Optional[List[str]] = None,
                                   output_format: str = "png",
                                   **kwargs) -> Dict[str, Any]:
        """Generate multiple complete stories in batch.

        Args:
            count: Number of stories to generate
            story_method: Story generation method
            image_service: Image generation service
            effects: Effects to apply (or None for auto-selection)
            output_format: Output format
            **kwargs: Additional parameters

        Returns:
            Batch generation result
        """
        self.total_steps = count * 4  # 4 steps per story
        self.completed_steps = 0

        # Create session directory
        session_dir = self.file_manager.create_session_directory()
        self.current_session = session_dir

        logger.info(f"Starting batch generation of {count} stories in session: {session_dir}")

        results = {
            "success": True,
            "session_directory": str(session_dir),
            "timestamp": datetime.now().isoformat(),
            "total_requested": count,
            "successful_generations": 0,
            "failed_generations": 0,
            "stories": [],
            "errors": []
        }

        for i in range(count):
            try:
                logger.info(f"Generating story {i + 1}/{count}")

                story_result = await self.generate_complete_story(
                    story_method=story_method,
                    image_service=image_service,
                    effects=effects,
                    output_format=output_format,
                    **kwargs
                )

                story_result["batch_index"] = i
                results["stories"].append(story_result)

                if story_result["success"]:
                    results["successful_generations"] += 1
                else:
                    results["failed_generations"] += 1
                    results["errors"].extend(story_result.get("errors", []))

            except Exception as e:
                error_msg = f"Batch item {i} failed: {str(e)}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                results["failed_generations"] += 1

        # Create batch summary
        batch_summary = {
            "batch_size": count,
            "successful": results["successful_generations"],
            "failed": results["failed_generations"],
            "success_rate": results["successful_generations"] / count * 100,
            "total_errors": len(results["errors"])
        }

        summary_file = self.file_manager.create_summary_report(batch_summary)
        results["batch_summary_file"] = str(summary_file)

        if results["failed_generations"] > 0:
            results["success"] = False

        logger.info(f"Batch generation completed: {results['successful_generations']}/{count} successful")
        return results

    async def create_story_video_sequence(self, story_count: int = 5,
                                        story_method: str = "random",
                                        duration_per_scene: float = 8.0,
                                        transition_duration: float = 1.0,
                                        audio_path: Optional[str] = None,
                                        **kwargs) -> Dict[str, Any]:
        """Create a video sequence from multiple generated stories.

        Args:
            story_count: Number of stories to generate for the sequence
            story_method: Story generation method
            duration_per_scene: Duration for each scene in seconds
            transition_duration: Duration of transitions between scenes
            audio_path: Optional background audio file
            **kwargs: Additional parameters

        Returns:
            Video sequence creation result
        """
        self.total_steps = story_count * 3 + 2  # Generate stories + images + create video + finalize
        self.completed_steps = 0

        # Create session directory
        session_dir = self.file_manager.create_session_directory()
        self.current_session = session_dir

        logger.info(f"Creating video sequence with {story_count} stories")

        result = {
            "success": False,
            "session_directory": str(session_dir),
            "timestamp": datetime.now().isoformat(),
            "story_count": story_count,
            "generated_stories": [],
            "generated_images": [],
            "video_path": None,
            "errors": []
        }

        try:
            # Generate stories and images
            image_paths = []
            effects_per_image = []

            for i in range(story_count):
                # Generate story
                self._update_progress(f"Generating story {i + 1}/{story_count}")

                story_result = await self.story_generator.generate_story(
                    method=story_method,
                    **kwargs
                )

                result["generated_stories"].append(story_result)

                # Generate image
                self._update_progress(f"Generating image {i + 1}/{story_count}")

                image_prompt = self.story_generator.generate_image_prompt(story_result)
                image_result = await self.image_generator.generate_image(
                    prompt=image_prompt,
                    story_data=story_result,
                    **kwargs
                )

                if image_result.get("success"):
                    result["generated_images"].append(image_result)
                    image_paths.append(image_result["saved_path"])

                    # Auto-select effects for this story
                    effects = self._auto_select_effects(story_result)
                    effects_per_image.append(effects)
                else:
                    logger.warning(f"Failed to generate image for story {i + 1}")
                    result["errors"].append(f"Image generation failed for story {i + 1}")

            if not image_paths:
                raise Exception("No images were successfully generated")

            # Create video sequence
            self._update_progress("Creating video sequence")

            video_result = self.effects_processor.create_video_sequence(
                image_paths=image_paths,
                effects_per_image=effects_per_image,
                duration_per_scene=duration_per_scene,
                transition_duration=transition_duration,
                audio_path=audio_path
            )

            if not video_result.get("success"):
                raise Exception(f"Video creation failed: {video_result.get('error')}")

            result["video_path"] = video_result["output_path"]
            result["video_duration"] = video_result.get("total_duration")
            result["has_audio"] = video_result.get("has_audio", False)

            # Finalize
            self._update_progress("Finalizing video sequence")

            # Create comprehensive summary
            summary_data = {
                "video_sequence": True,
                "story_count": story_count,
                "successful_stories": len(result["generated_stories"]),
                "successful_images": len(result["generated_images"]),
                "video_path": result["video_path"],
                "video_duration": result.get("video_duration"),
                "has_audio": result.get("has_audio"),
                "story_titles": [story.get("title", "Untitled") for story in result["generated_stories"]]
            }

            summary_file = self.file_manager.create_summary_report(summary_data)
            result["summary_file"] = str(summary_file)

            result["success"] = True
            logger.info(f"Video sequence created successfully: {result['video_path']}")

        except Exception as e:
            error_msg = f"Video sequence creation failed: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            result["success"] = False

        return result

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status and statistics.

        Returns:
            Pipeline status information
        """
        status = {
            "pipeline_initialized": True,
            "current_session": str(self.current_session) if self.current_session else None,
            "progress": {
                "completed_steps": self.completed_steps,
                "total_steps": self.total_steps,
                "percentage": (self.completed_steps / self.total_steps * 100) if self.total_steps > 0 else 0
            },
            "components": {
                "story_generator": {
                    "available_methods": list(self.story_generator.generation_methods.keys()),
                    "openai_available": self.story_generator.openai_client is not None
                },
                "image_generator": {
                    "available_services": self.image_generator.service_priority,
                    "openai_available": self.image_generator.openai_client is not None,
                    "stability_available": self.image_generator.stability_client is not None
                },
                "effects_processor": {
                    "available_effects": self.effects_processor.get_available_effects(),
                    "supported_formats": self.effects_processor.supported_formats
                }
            },
            "configuration": {
                "output_directory": str(self.file_manager.base_output_dir),
                "api_keys_configured": len(self.config.get_api_keys())
            }
        }

        return status

    def validate_pipeline_setup(self) -> Dict[str, Any]:
        """Validate pipeline setup and configuration.

        Returns:
            Validation result with recommendations
        """
        validation = {
            "valid": True,
            "issues": [],
            "warnings": [],
            "recommendations": []
        }

        # Check API keys
        api_keys = self.config.get_api_keys()
        if not api_keys.get('openai'):
            validation["warnings"].append("OpenAI API key not configured - story generation and DALL-E will be unavailable")
            validation["recommendations"].append("Add OPENAI_API_KEY to environment variables")

        if not api_keys.get('stability'):
            validation["warnings"].append("Stability AI API key not configured - Stable Diffusion will be unavailable")
            validation["recommendations"].append("Add STABILITY_API_KEY to environment variables for fallback image generation")

        # Check output directory
        try:
            self.file_manager.base_output_dir.mkdir(exist_ok=True)
        except Exception as e:
            validation["issues"].append(f"Cannot create output directory: {e}")
            validation["valid"] = False

        # Check component initialization
        if not self.story_generator.openai_client and not self.story_generator.template_data:
            validation["issues"].append("No story generation methods available")
            validation["valid"] = False

        if not self.image_generator.openai_client and not self.image_generator.stability_client:
            validation["issues"].append("No image generation services available")
            validation["valid"] = False

        # Performance recommendations
        if len(api_keys) == 1:
            validation["recommendations"].append("Configure multiple API services for better reliability")

        validation["recommendations"].append("Ensure sufficient disk space for output files")
        validation["recommendations"].append("Consider using SSD storage for better performance")

        return validation

    async def cleanup_session(self, session_path: Optional[str] = None):
        """Clean up temporary files from a session.

        Args:
            session_path: Path to session directory, or None for current session
        """
        if session_path:
            cleanup_path = Path(session_path)
        elif self.current_session:
            cleanup_path = self.current_session
        else:
            logger.warning("No session to clean up")
            return

        try:
            # Clean up temporary files
            temp_files = list(cleanup_path.glob("**/temp_*"))
            for temp_file in temp_files:
                temp_file.unlink()
                logger.debug(f"Removed temp file: {temp_file}")

            logger.info(f"Session cleanup completed: {cleanup_path}")

        except Exception as e:
            logger.error(f"Session cleanup failed: {e}")

    def export_session_data(self, session_path: Optional[str] = None,
                          export_format: str = "json") -> str:
        """Export session data for sharing or backup.

        Args:
            session_path: Path to session directory
            export_format: Export format (json, zip)

        Returns:
            Path to exported file
        """
        if session_path:
            source_path = Path(session_path)
        elif self.current_session:
            source_path = self.current_session
        else:
            raise ValueError("No session to export")

        if not source_path.exists():
            raise FileNotFoundError(f"Session directory not found: {source_path}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if export_format == "json":
            # Export metadata as JSON
            export_path = source_path.parent / f"session_export_{timestamp}.json"

            # Collect all metadata
            export_data = {
                "session_info": {
                    "path": str(source_path),
                    "created": timestamp,
                    "export_format": export_format
                },
                "files": {}
            }

            # Add file information
            for file_path in source_path.rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(source_path)
                    export_data["files"][str(relative_path)] = {
                        "size": file_path.stat().st_size,
                        "modified": file_path.stat().st_mtime
                    }

            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)

        elif export_format == "zip":
            # Create ZIP archive
            import shutil
            export_path = source_path.parent / f"session_export_{timestamp}.zip"
            shutil.make_archive(str(export_path.with_suffix('')), 'zip', source_path)

        else:
            raise ValueError(f"Unsupported export format: {export_format}")

        logger.info(f"Session exported: {export_path}")
        return str(export_path)
