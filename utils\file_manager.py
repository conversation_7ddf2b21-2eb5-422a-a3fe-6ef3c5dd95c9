"""
File Management Utilities

Handles file operations, directory structure creation, and output organization.
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import shutil

logger = logging.getLogger(__name__)


class FileManager:
    """Manages file operations and output organization."""
    
    def __init__(self, base_output_dir: str = "output"):
        """Initialize file manager.
        
        Args:
            base_output_dir: Base directory for all outputs
        """
        self.base_output_dir = Path(base_output_dir)
        self.current_session_dir = None
    
    def create_session_directory(self, timestamp: Optional[str] = None) -> Path:
        """Create a new session directory with timestamp.
        
        Args:
            timestamp: Custom timestamp string, or None for current time
            
        Returns:
            Path to created session directory
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        session_dir = self.base_output_dir / timestamp
        session_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        subdirs = ["stories", "images", "processed", "metadata"]
        for subdir in subdirs:
            (session_dir / subdir).mkdir(exist_ok=True)
        
        self.current_session_dir = session_dir
        logger.info(f"Created session directory: {session_dir}")
        
        return session_dir
    
    def get_session_dir(self) -> Optional[Path]:
        """Get current session directory."""
        return self.current_session_dir
    
    def save_story_data(self, story_data: Dict[str, Any], filename: Optional[str] = None) -> Path:
        """Save story data to JSON file.
        
        Args:
            story_data: Story data dictionary
            filename: Custom filename, or None for auto-generated
            
        Returns:
            Path to saved file
        """
        if not self.current_session_dir:
            self.create_session_directory()
        
        if filename is None:
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"story_{timestamp}.json"
        
        filepath = self.current_session_dir / "stories" / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(story_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved story data: {filepath}")
        return filepath
    
    def save_image_metadata(self, image_data: Dict[str, Any], image_filename: str) -> Path:
        """Save image metadata to JSON file.
        
        Args:
            image_data: Image generation metadata
            image_filename: Name of the associated image file
            
        Returns:
            Path to saved metadata file
        """
        if not self.current_session_dir:
            self.create_session_directory()
        
        metadata_filename = f"{Path(image_filename).stem}_metadata.json"
        filepath = self.current_session_dir / "metadata" / metadata_filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(image_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved image metadata: {filepath}")
        return filepath
    
    def get_unique_filename(self, directory: Path, base_name: str, extension: str) -> str:
        """Generate unique filename to avoid conflicts.
        
        Args:
            directory: Target directory
            base_name: Base filename without extension
            extension: File extension (with or without dot)
            
        Returns:
            Unique filename
        """
        if not extension.startswith('.'):
            extension = '.' + extension
        
        counter = 1
        filename = f"{base_name}{extension}"
        
        while (directory / filename).exists():
            filename = f"{base_name}_{counter:03d}{extension}"
            counter += 1
        
        return filename
    
    def save_processed_output(self, file_path: str, output_type: str = "processed") -> Path:
        """Move processed file to appropriate output directory.
        
        Args:
            file_path: Path to processed file
            output_type: Type of output (processed, final, etc.)
            
        Returns:
            New path in output directory
        """
        if not self.current_session_dir:
            self.create_session_directory()
        
        source_path = Path(file_path)
        target_dir = self.current_session_dir / output_type
        target_dir.mkdir(exist_ok=True)
        
        # Generate unique filename
        unique_filename = self.get_unique_filename(
            target_dir, 
            source_path.stem, 
            source_path.suffix
        )
        
        target_path = target_dir / unique_filename
        
        # Copy or move file
        if source_path.exists():
            shutil.copy2(source_path, target_path)
            logger.info(f"Saved processed output: {target_path}")
        else:
            logger.error(f"Source file not found: {source_path}")
            raise FileNotFoundError(f"Source file not found: {source_path}")
        
        return target_path
    
    def create_summary_report(self, session_data: Dict[str, Any]) -> Path:
        """Create summary report for the session.
        
        Args:
            session_data: Session summary data
            
        Returns:
            Path to summary report
        """
        if not self.current_session_dir:
            raise ValueError("No active session directory")
        
        report_path = self.current_session_dir / "session_summary.json"
        
        # Add session metadata
        session_data.update({
            "session_directory": str(self.current_session_dir),
            "created_at": datetime.now().isoformat(),
            "total_files": self._count_session_files()
        })
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created session summary: {report_path}")
        return report_path
    
    def _count_session_files(self) -> Dict[str, int]:
        """Count files in each session subdirectory."""
        if not self.current_session_dir:
            return {}
        
        counts = {}
        for subdir in ["stories", "images", "processed", "metadata"]:
            subdir_path = self.current_session_dir / subdir
            if subdir_path.exists():
                counts[subdir] = len(list(subdir_path.glob("*")))
            else:
                counts[subdir] = 0
        
        return counts
    
    def cleanup_temp_files(self, temp_dir: str = "temp"):
        """Clean up temporary files.
        
        Args:
            temp_dir: Temporary directory to clean
        """
        temp_path = Path(temp_dir)
        if temp_path.exists():
            try:
                shutil.rmtree(temp_path)
                logger.info(f"Cleaned up temporary directory: {temp_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temp directory {temp_path}: {e}")
    
    def get_session_files(self, file_type: Optional[str] = None) -> List[Path]:
        """Get list of files in current session.
        
        Args:
            file_type: Filter by file type (stories, images, processed, metadata)
            
        Returns:
            List of file paths
        """
        if not self.current_session_dir:
            return []
        
        if file_type:
            search_dir = self.current_session_dir / file_type
            if search_dir.exists():
                return list(search_dir.glob("*"))
            else:
                return []
        else:
            # Return all files in session
            all_files = []
            for subdir in ["stories", "images", "processed", "metadata"]:
                subdir_path = self.current_session_dir / subdir
                if subdir_path.exists():
                    all_files.extend(list(subdir_path.glob("*")))
            return all_files


def create_output_structure(base_dir: str = "output") -> Path:
    """Create the basic output directory structure.
    
    Args:
        base_dir: Base output directory name
        
    Returns:
        Path to created base directory
    """
    base_path = Path(base_dir)
    base_path.mkdir(exist_ok=True)
    
    # Create example subdirectories
    subdirs = ["examples", "templates", "cache"]
    for subdir in subdirs:
        (base_path / subdir).mkdir(exist_ok=True)
    
    logger.info(f"Created output structure at: {base_path}")
    return base_path


def ensure_directory_exists(directory: str) -> Path:
    """Ensure directory exists, create if necessary.
    
    Args:
        directory: Directory path
        
    Returns:
        Path object for the directory
    """
    dir_path = Path(directory)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path
