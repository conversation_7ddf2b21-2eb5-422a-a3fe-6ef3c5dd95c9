# Storytelling Pipeline Configuration

# Story Generation Settings
story_generation:
  default_method: "openai"  # openai, template, interactive, random
  openai:
    model: "gpt-4"
    max_tokens: 500
    temperature: 0.8
    system_prompt: "You are a creative storytelling assistant that generates vivid, detailed story prompts suitable for visual representation."
  
  template:
    genres: ["fantasy", "sci-fi", "mystery", "romance", "adventure", "horror"]
    settings: ["ancient temple", "futuristic city", "enchanted forest", "space station", "medieval castle", "underwater realm"]
    characters: ["wise sage", "brave warrior", "mysterious stranger", "young apprentice", "ancient guardian", "cosmic entity"]
    conflicts: ["quest for truth", "battle against darkness", "journey of discovery", "test of courage", "search for redemption", "fight for survival"]
  
  validation:
    min_length: 50
    max_length: 1000
    content_filter: true

# Image Generation Settings
image_generation:
  primary_service: "openai"  # openai, stability, huggingface
  fallback_service: "stability"
  
  openai:
    model: "dall-e-3"
    size: "1024x1024"
    quality: "hd"
    style: "vivid"
  
  stability:
    model: "stable-diffusion-xl-1024-v1-0"
    steps: 30
    cfg_scale: 7
  
  prompt_enhancement:
    style_keywords: ["cinematic", "highly detailed", "8K resolution", "professional photography"]
    lighting_keywords: ["dramatic lighting", "golden hour", "soft shadows"]
    quality_keywords: ["masterpiece", "best quality", "ultra-detailed"]

# Visual Effects Settings
effects_processing:
  default_intensity: "medium"  # low, medium, high
  
  color_effects:
    contrast_enhancement: 1.2
    saturation_boost: 1.15
    brightness_adjustment: 1.05
  
  artistic_effects:
    available: ["oil_painting", "watercolor", "sketch", "hdr", "vintage"]
    default: "hdr"
  
  motion_effects:
    ken_burns_zoom: 1.1
    duration_per_scene: 8
    transition_duration: 1
  
  output_formats:
    static: ["png", "jpg"]
    animated: ["gif", "mp4"]
    video_settings:
      fps: 24
      bitrate: "5M"
      codec: "libx264"

# Pipeline Settings
pipeline:
  output_structure:
    base_dir: "output"
    timestamp_format: "%Y%m%d_%H%M%S"
    subdirs: ["stories", "images", "processed"]
  
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "storytelling_pipeline.log"
    max_size_mb: 10
    backup_count: 5
  
  error_handling:
    retry_attempts: 3
    retry_delay: 2
    graceful_degradation: true
  
  performance:
    batch_size: 5
    concurrent_requests: 3
    cache_enabled: true
    cache_ttl_hours: 24

# CLI Settings
cli:
  default_batch_size: 1
  progress_bar: true
  verbose: false
  interactive_prompts: true
