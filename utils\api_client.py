"""
API Client Utilities

Provides robust API clients with retry logic, rate limiting, and error handling.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import aiohttp
import openai
from openai import OpenAI
import requests

logger = logging.getLogger(__name__)


class APIClient(ABC):
    """Abstract base class for API clients."""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None, 
                 max_retries: int = 3, retry_delay: float = 2.0):
        """Initialize API client.
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for API endpoints
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        """
        self.api_key = api_key
        self.base_url = base_url
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate content using the API."""
        pass
    
    async def _make_request_with_retry(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic."""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if not self.session:
                    self.session = aiohttp.ClientSession()
                
                async with self.session.request(method, url, **kwargs) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:  # Rate limited
                        wait_time = self.retry_delay * (2 ** attempt)
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        response.raise_for_status()
                        
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
        
        raise last_exception or Exception("Request failed after all retries")


class OpenAIClient(APIClient):
    """OpenAI API client with retry logic and error handling."""
    
    def __init__(self, api_key: str, organization_id: Optional[str] = None, **kwargs):
        """Initialize OpenAI client.
        
        Args:
            api_key: OpenAI API key
            organization_id: OpenAI organization ID (optional)
            **kwargs: Additional arguments for base APIClient
        """
        super().__init__(api_key, **kwargs)
        self.organization_id = organization_id
        self.client = OpenAI(
            api_key=api_key,
            organization=organization_id
        )
    
    async def generate_story(self, prompt: str, model: str = "gpt-4", 
                           max_tokens: int = 500, temperature: float = 0.8) -> Dict[str, Any]:
        """Generate story using OpenAI GPT.
        
        Args:
            prompt: Story generation prompt
            model: GPT model to use
            max_tokens: Maximum tokens in response
            temperature: Creativity temperature (0-1)
            
        Returns:
            Generated story data
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a creative storytelling assistant that generates vivid, detailed story prompts suitable for visual representation."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            story_text = response.choices[0].message.content
            
            return {
                "success": True,
                "story": story_text,
                "model": model,
                "tokens_used": response.usage.total_tokens,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"OpenAI story generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    async def generate_image(self, prompt: str, size: str = "1024x1024", 
                           quality: str = "hd", style: str = "vivid") -> Dict[str, Any]:
        """Generate image using DALL-E 3.
        
        Args:
            prompt: Image generation prompt
            size: Image size (1024x1024, 1792x1024, 1024x1792)
            quality: Image quality (standard, hd)
            style: Image style (vivid, natural)
            
        Returns:
            Generated image data
        """
        try:
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=size,
                quality=quality,
                style=style,
                n=1
            )
            
            return {
                "success": True,
                "image_url": response.data[0].url,
                "revised_prompt": response.data[0].revised_prompt,
                "size": size,
                "quality": quality,
                "style": style,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"OpenAI image generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    async def generate_content(self, prompt: str, content_type: str = "story", **kwargs) -> Dict[str, Any]:
        """Generate content using OpenAI API.
        
        Args:
            prompt: Generation prompt
            content_type: Type of content (story, image)
            **kwargs: Additional parameters
            
        Returns:
            Generated content data
        """
        if content_type == "story":
            return await self.generate_story(prompt, **kwargs)
        elif content_type == "image":
            return await self.generate_image(prompt, **kwargs)
        else:
            raise ValueError(f"Unsupported content type: {content_type}")


class StabilityClient(APIClient):
    """Stability AI API client for Stable Diffusion."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize Stability AI client."""
        super().__init__(api_key, base_url="https://api.stability.ai", **kwargs)
    
    async def generate_image(self, prompt: str, model: str = "stable-diffusion-xl-1024-v1-0",
                           width: int = 1024, height: int = 1024, steps: int = 30,
                           cfg_scale: float = 7.0) -> Dict[str, Any]:
        """Generate image using Stability AI.
        
        Args:
            prompt: Image generation prompt
            model: Model to use
            width: Image width
            height: Image height
            steps: Number of diffusion steps
            cfg_scale: CFG scale for prompt adherence
            
        Returns:
            Generated image data
        """
        url = f"{self.base_url}/v1/generation/{model}/text-to-image"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "text_prompts": [{"text": prompt}],
            "cfg_scale": cfg_scale,
            "height": height,
            "width": width,
            "steps": steps,
            "samples": 1
        }
        
        try:
            result = await self._make_request_with_retry("POST", url, headers=headers, json=data)
            
            if result.get("artifacts"):
                return {
                    "success": True,
                    "image_data": result["artifacts"][0]["base64"],
                    "model": model,
                    "prompt": prompt,
                    "width": width,
                    "height": height,
                    "steps": steps,
                    "cfg_scale": cfg_scale,
                    "timestamp": time.time()
                }
            else:
                return {
                    "success": False,
                    "error": "No image artifacts returned",
                    "timestamp": time.time()
                }
                
        except Exception as e:
            logger.error(f"Stability AI image generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate image content using Stability AI."""
        return await self.generate_image(prompt, **kwargs)
