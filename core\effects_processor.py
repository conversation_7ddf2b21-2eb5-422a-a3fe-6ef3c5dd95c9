"""
Enhanced Visual Effects Processing Module

Extends existing video generation capabilities into comprehensive EffectsProcessor class
with color effects, artistic filters, motion effects, and multiple output formats.
"""

import cv2
import numpy as np
import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
from PIL import Image, ImageEnhance, ImageFilter
from moviepy.editor import (
    ImageClip, CompositeVideoClip, concatenate_videoclips,
    AudioFileClip, VideoFileClip
)
import sys
import os

# Add parent directory to path to import existing video generators
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import load_config
from utils.validators import validate_effects_config
from utils.file_manager import FileManager

logger = logging.getLogger(__name__)


class EffectsProcessor:
    """Comprehensive visual effects processor with multiple output formats."""

    def __init__(self, config_manager=None, file_manager=None):
        """Initialize the effects processor.

        Args:
            config_manager: Configuration manager instance
            file_manager: File manager instance
        """
        self.config = config_manager or load_config()
        self.file_manager = file_manager or FileManager()
        self.effects_config = self.config.get_effects_config()

        # Load effect presets
        self.effect_presets = self._load_effect_presets()

        # Supported formats
        self.supported_formats = {
            "static": ["png", "jpg", "jpeg"],
            "animated": ["gif", "mp4", "webm"]
        }

    def _load_effect_presets(self) -> Dict[str, Dict[str, Any]]:
        """Load effect presets from configuration."""
        return {
            "color_effects": {
                "vintage": {
                    "contrast": 1.3,
                    "saturation": 0.8,
                    "brightness": 1.1,
                    "sepia": True
                },
                "vibrant": {
                    "contrast": 1.4,
                    "saturation": 1.5,
                    "brightness": 1.05,
                    "color_boost": True
                },
                "dramatic": {
                    "contrast": 1.6,
                    "saturation": 1.2,
                    "brightness": 0.9,
                    "shadows": True
                },
                "soft": {
                    "contrast": 0.9,
                    "saturation": 1.1,
                    "brightness": 1.15,
                    "blur": 0.5
                }
            },
            "artistic_effects": {
                "oil_painting": {
                    "bilateral_filter": True,
                    "edge_enhancement": True,
                    "color_quantization": 8
                },
                "watercolor": {
                    "gaussian_blur": 2,
                    "edge_preserving": True,
                    "color_reduction": True
                },
                "sketch": {
                    "edge_detection": True,
                    "grayscale": True,
                    "invert": True
                },
                "hdr": {
                    "tone_mapping": True,
                    "contrast_enhancement": True,
                    "detail_enhancement": True
                }
            },
            "motion_effects": {
                "ken_burns": {
                    "zoom_factor": 1.1,
                    "pan_direction": "center"
                },
                "parallax": {
                    "layers": 3,
                    "depth_factor": 0.1
                },
                "zoom_in": {
                    "zoom_factor": 1.2,
                    "duration": 8
                },
                "fade_transition": {
                    "fade_duration": 1.0,
                    "fade_type": "cross"
                }
            }
        }

    async def process_image(self, image_path: str, effects: List[str],
                          output_format: str = "png", intensity: str = "medium",
                          **kwargs) -> Dict[str, Any]:
        """Process image with specified effects.

        Args:
            image_path: Path to input image
            effects: List of effects to apply
            output_format: Output format (png, jpg, gif, mp4)
            intensity: Effect intensity (low, medium, high)
            **kwargs: Additional processing parameters

        Returns:
            Processing result with output path and metadata
        """
        # Validate inputs
        if not Path(image_path).exists():
            raise FileNotFoundError(f"Input image not found: {image_path}")

        effects_config = {
            "effects": effects,
            "output_format": output_format,
            "intensity": intensity,
            **kwargs
        }

        validation_result = validate_effects_config(effects_config)
        if not validation_result["valid"]:
            raise ValueError(f"Invalid effects configuration: {validation_result['errors']}")

        logger.info(f"Processing image with effects: {effects}")

        try:
            # Load image
            image = self._load_image(image_path)

            # Apply effects
            processed_image = await self._apply_effects(image, effects, intensity)

            # Generate output
            if output_format in self.supported_formats["static"]:
                output_path = await self._save_static_image(processed_image, output_format)
            elif output_format in self.supported_formats["animated"]:
                output_path = await self._create_animated_output(
                    image, processed_image, output_format, **kwargs
                )
            else:
                raise ValueError(f"Unsupported output format: {output_format}")

            # Create result
            result = {
                "success": True,
                "input_path": image_path,
                "output_path": str(output_path),
                "effects_applied": effects,
                "output_format": output_format,
                "intensity": intensity,
                "processing_time": time.time(),
                "validation": validation_result
            }

            logger.info(f"Image processing completed: {output_path}")
            return result

        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "input_path": image_path,
                "effects_requested": effects
            }

    def _load_image(self, image_path: str) -> np.ndarray:
        """Load image as numpy array."""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Failed to load image: {image_path}")
        return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    async def _apply_effects(self, image: np.ndarray, effects: List[str],
                           intensity: str) -> np.ndarray:
        """Apply list of effects to image.

        Args:
            image: Input image as numpy array
            effects: List of effect names
            intensity: Effect intensity level

        Returns:
            Processed image
        """
        processed = image.copy()
        intensity_multiplier = {"low": 0.5, "medium": 1.0, "high": 1.5}[intensity]

        for effect in effects:
            if effect in self.effect_presets["color_effects"]:
                processed = self._apply_color_effect(processed, effect, intensity_multiplier)
            elif effect in self.effect_presets["artistic_effects"]:
                processed = self._apply_artistic_effect(processed, effect, intensity_multiplier)
            else:
                logger.warning(f"Unknown effect: {effect}")

        return processed

    def _apply_color_effect(self, image: np.ndarray, effect: str,
                          intensity: float) -> np.ndarray:
        """Apply color-based effects."""
        preset = self.effect_presets["color_effects"][effect]

        # Convert to PIL for easier manipulation
        pil_image = Image.fromarray(image)

        # Apply contrast
        if "contrast" in preset:
            contrast_factor = 1 + (preset["contrast"] - 1) * intensity
            pil_image = ImageEnhance.Contrast(pil_image).enhance(contrast_factor)

        # Apply saturation
        if "saturation" in preset:
            saturation_factor = 1 + (preset["saturation"] - 1) * intensity
            pil_image = ImageEnhance.Color(pil_image).enhance(saturation_factor)

        # Apply brightness
        if "brightness" in preset:
            brightness_factor = 1 + (preset["brightness"] - 1) * intensity
            pil_image = ImageEnhance.Brightness(pil_image).enhance(brightness_factor)

        # Apply sepia effect
        if preset.get("sepia"):
            pil_image = self._apply_sepia(pil_image, intensity)

        # Apply blur if specified
        if preset.get("blur"):
            blur_radius = preset["blur"] * intensity
            pil_image = pil_image.filter(ImageFilter.GaussianBlur(radius=blur_radius))

        return np.array(pil_image)

    def _apply_artistic_effect(self, image: np.ndarray, effect: str,
                             intensity: float) -> np.ndarray:
        """Apply artistic effects using OpenCV."""
        preset = self.effect_presets["artistic_effects"][effect]
        processed = image.copy()

        if effect == "oil_painting":
            # Oil painting effect using bilateral filter
            processed = cv2.bilateralFilter(processed, 15, 80, 80)
            if preset.get("edge_enhancement"):
                # Enhance edges
                gray = cv2.cvtColor(processed, cv2.COLOR_RGB2GRAY)
                edges = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                            cv2.THRESH_BINARY, 9, 9)
                edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
                processed = cv2.bitwise_and(processed, edges)

        elif effect == "watercolor":
            # Watercolor effect
            if preset.get("gaussian_blur"):
                blur_size = int(preset["gaussian_blur"] * intensity)
                processed = cv2.GaussianBlur(processed, (blur_size*2+1, blur_size*2+1), 0)

            if preset.get("edge_preserving"):
                processed = cv2.edgePreservingFilter(processed, flags=1, sigma_s=50, sigma_r=0.4)

        elif effect == "sketch":
            # Sketch effect
            gray = cv2.cvtColor(processed, cv2.COLOR_RGB2GRAY)
            gray_blur = cv2.medianBlur(gray, 5)
            edges = cv2.adaptiveThreshold(gray_blur, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                        cv2.THRESH_BINARY, 9, 9)
            processed = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)

        elif effect == "hdr":
            # HDR effect using tone mapping
            processed = processed.astype(np.float32) / 255.0
            # Simple tone mapping
            processed = np.power(processed, 0.6) * 1.2
            processed = np.clip(processed * 255, 0, 255).astype(np.uint8)

        return processed

    def _apply_sepia(self, image: Image.Image, intensity: float) -> Image.Image:
        """Apply sepia tone effect."""
        # Convert to numpy for sepia transformation
        img_array = np.array(image)

        # Sepia transformation matrix
        sepia_filter = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])

        # Apply sepia with intensity
        sepia_img = img_array @ sepia_filter.T
        sepia_img = np.clip(sepia_img, 0, 255)

        # Blend with original based on intensity
        blended = img_array * (1 - intensity) + sepia_img * intensity
        blended = np.clip(blended, 0, 255).astype(np.uint8)

        return Image.fromarray(blended)

    async def _save_static_image(self, image: np.ndarray, format: str) -> Path:
        """Save processed image as static file."""
        if not self.file_manager.get_session_dir():
            self.file_manager.create_session_directory()

        # Generate filename
        timestamp = int(time.time())
        filename = f"processed_image_{timestamp}.{format}"

        output_dir = self.file_manager.get_session_dir() / "processed"
        output_path = output_dir / filename

        # Convert and save
        pil_image = Image.fromarray(image)
        if format.lower() == "jpg" or format.lower() == "jpeg":
            pil_image = pil_image.convert("RGB")

        pil_image.save(output_path, format.upper())

        return output_path

    async def _create_animated_output(self, original_image: np.ndarray,
                                    processed_image: np.ndarray, format: str,
                                    duration: float = 8.0, fps: int = 24,
                                    motion_effect: str = "ken_burns", **kwargs) -> Path:
        """Create animated output (GIF or MP4) with motion effects.

        Args:
            original_image: Original image
            processed_image: Processed image
            format: Output format (gif, mp4)
            duration: Animation duration in seconds
            fps: Frames per second
            motion_effect: Type of motion effect to apply
            **kwargs: Additional parameters

        Returns:
            Path to created animated file
        """
        if not self.file_manager.get_session_dir():
            self.file_manager.create_session_directory()

        # Generate filename
        timestamp = int(time.time())
        filename = f"animated_{motion_effect}_{timestamp}.{format}"

        output_dir = self.file_manager.get_session_dir() / "processed"
        output_path = output_dir / filename

        # Save processed image temporarily
        temp_image_path = output_dir / f"temp_processed_{timestamp}.png"
        Image.fromarray(processed_image).save(temp_image_path)

        try:
            # Create video clip
            clip = ImageClip(str(temp_image_path)).set_duration(duration)

            # Apply motion effects
            if motion_effect == "ken_burns":
                clip = self._apply_ken_burns_effect(clip, **kwargs)
            elif motion_effect == "zoom_in":
                clip = self._apply_zoom_effect(clip, zoom_factor=kwargs.get("zoom_factor", 1.2))
            elif motion_effect == "fade_transition":
                clip = self._apply_fade_effect(clip, **kwargs)

            # Resize for output
            clip = clip.resize(width=1080)

            # Export based on format
            if format == "gif":
                clip.write_gif(str(output_path), fps=min(fps, 15))  # Limit GIF FPS
            elif format == "mp4":
                clip.write_videofile(str(output_path), fps=fps, codec='libx264')

            # Cleanup
            if temp_image_path.exists():
                temp_image_path.unlink()

            return output_path

        except Exception as e:
            # Cleanup on error
            if temp_image_path.exists():
                temp_image_path.unlink()
            raise e

    def _apply_ken_burns_effect(self, clip, zoom_factor: float = 1.1,
                              pan_direction: str = "center") -> ImageClip:
        """Apply Ken Burns effect (slow zoom and pan)."""
        def ken_burns_transform(get_frame, t):
            frame = get_frame(t)
            progress = t / clip.duration

            # Calculate zoom
            current_zoom = 1 + (zoom_factor - 1) * progress

            # Apply zoom
            h, w = frame.shape[:2]
            new_h, new_w = int(h * current_zoom), int(w * current_zoom)

            # Resize frame
            resized = cv2.resize(frame, (new_w, new_h))

            # Calculate crop to maintain original size
            if pan_direction == "center":
                start_x = (new_w - w) // 2
                start_y = (new_h - h) // 2
            elif pan_direction == "left":
                start_x = 0
                start_y = (new_h - h) // 2
            elif pan_direction == "right":
                start_x = new_w - w
                start_y = (new_h - h) // 2
            else:  # center
                start_x = (new_w - w) // 2
                start_y = (new_h - h) // 2

            # Crop to original size
            cropped = resized[start_y:start_y + h, start_x:start_x + w]

            return cropped

        return clip.fl(ken_burns_transform)

    def _apply_zoom_effect(self, clip, zoom_factor: float = 1.2) -> ImageClip:
        """Apply simple zoom effect."""
        return clip.resize(zoom_factor)

    def _apply_fade_effect(self, clip, fade_duration: float = 1.0) -> ImageClip:
        """Apply fade in/out effect."""
        return clip.fadein(fade_duration).fadeout(fade_duration)

    async def process_batch(self, image_paths: List[str], effects: List[str],
                          output_format: str = "png", **kwargs) -> List[Dict[str, Any]]:
        """Process multiple images with the same effects.

        Args:
            image_paths: List of input image paths
            effects: Effects to apply to all images
            output_format: Output format for all images
            **kwargs: Additional processing parameters

        Returns:
            List of processing results
        """
        results = []

        for i, image_path in enumerate(image_paths):
            try:
                logger.info(f"Processing batch item {i + 1}/{len(image_paths)}: {image_path}")

                result = await self.process_image(
                    image_path=image_path,
                    effects=effects,
                    output_format=output_format,
                    **kwargs
                )

                result["batch_index"] = i
                results.append(result)

            except Exception as e:
                logger.error(f"Batch item {i} failed: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "input_path": image_path,
                    "batch_index": i
                })

        logger.info(f"Batch processing completed: {len(results)} items processed")
        return results

    def create_video_sequence(self, image_paths: List[str], effects_per_image: List[List[str]],
                            duration_per_scene: float = 8.0, transition_duration: float = 1.0,
                            audio_path: Optional[str] = None) -> Dict[str, Any]:
        """Create video sequence from multiple processed images.

        Args:
            image_paths: List of input image paths
            effects_per_image: List of effects for each image
            duration_per_scene: Duration for each scene
            transition_duration: Duration of transitions between scenes
            audio_path: Optional audio file path

        Returns:
            Video creation result
        """
        if len(image_paths) != len(effects_per_image):
            raise ValueError("Number of images must match number of effect lists")

        try:
            clips = []

            # Process each image and create clip
            for i, (image_path, effects) in enumerate(zip(image_paths, effects_per_image)):
                logger.info(f"Creating clip {i + 1}/{len(image_paths)}")

                # Process image
                processed_result = asyncio.run(self.process_image(
                    image_path=image_path,
                    effects=effects,
                    output_format="png"
                ))

                if not processed_result["success"]:
                    logger.warning(f"Failed to process image {i}: {processed_result.get('error')}")
                    continue

                # Create clip
                clip = ImageClip(processed_result["output_path"]).set_duration(duration_per_scene)

                # Apply Ken Burns effect
                clip = self._apply_ken_burns_effect(clip)

                # Add fade transitions
                if i > 0:  # Not first clip
                    clip = clip.fadein(transition_duration)
                if i < len(image_paths) - 1:  # Not last clip
                    clip = clip.fadeout(transition_duration)

                clips.append(clip)

            if not clips:
                raise Exception("No clips were successfully created")

            # Concatenate clips
            final_video = concatenate_videoclips(clips, method="compose")

            # Add audio if provided
            if audio_path and Path(audio_path).exists():
                audio = AudioFileClip(audio_path).set_duration(final_video.duration)
                final_video = final_video.set_audio(audio)

            # Generate output path
            if not self.file_manager.get_session_dir():
                self.file_manager.create_session_directory()

            timestamp = int(time.time())
            output_path = self.file_manager.get_session_dir() / "processed" / f"video_sequence_{timestamp}.mp4"

            # Write video
            final_video.write_videofile(str(output_path), fps=24, codec='libx264')

            return {
                "success": True,
                "output_path": str(output_path),
                "total_clips": len(clips),
                "total_duration": final_video.duration,
                "has_audio": audio_path is not None
            }

        except Exception as e:
            logger.error(f"Video sequence creation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_available_effects(self) -> Dict[str, List[str]]:
        """Get list of available effects by category.

        Returns:
            Dictionary of effect categories and their available effects
        """
        return {
            "color_effects": list(self.effect_presets["color_effects"].keys()),
            "artistic_effects": list(self.effect_presets["artistic_effects"].keys()),
            "motion_effects": list(self.effect_presets["motion_effects"].keys())
        }

    def preview_effect(self, image_path: str, effect: str, intensity: str = "medium") -> str:
        """Create a quick preview of an effect on an image.

        Args:
            image_path: Path to input image
            effect: Effect name to preview
            intensity: Effect intensity

        Returns:
            Path to preview image
        """
        try:
            # Load and process image
            image = self._load_image(image_path)
            processed = asyncio.run(self._apply_effects(image, [effect], intensity))

            # Save preview
            timestamp = int(time.time())
            preview_path = f"preview_{effect}_{timestamp}.png"

            Image.fromarray(processed).save(preview_path)

            logger.info(f"Effect preview created: {preview_path}")
            return preview_path

        except Exception as e:
            logger.error(f"Preview creation failed: {e}")
            raise
