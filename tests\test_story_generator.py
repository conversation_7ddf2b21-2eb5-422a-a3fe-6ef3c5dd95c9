"""
Unit Tests for Story Generator

Tests for the enhanced story prompt generator functionality.
"""

import pytest
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from core.story_generator import StoryPromptGenerator
from config.settings import ConfigManager


class TestStoryPromptGenerator:
    """Test cases for StoryPromptGenerator class."""
    
    @pytest.fixture
    def generator(self):
        """Create a story generator instance for testing."""
        # Create minimal config for testing
        test_config = ConfigManager()
        return StoryPromptGenerator(test_config)
    
    def test_initialization(self, generator):
        """Test generator initialization."""
        assert generator is not None
        assert hasattr(generator, 'generation_methods')
        assert hasattr(generator, 'template_data')
        assert len(generator.generation_methods) > 0
    
    def test_template_data_loading(self, generator):
        """Test template data is loaded correctly."""
        template_data = generator.template_data
        
        required_keys = ['genres', 'settings', 'characters', 'conflicts', 'moods', 'visual_styles']
        for key in required_keys:
            assert key in template_data
            assert len(template_data[key]) > 0
    
    @pytest.mark.asyncio
    async def test_template_generation(self, generator):
        """Test template-based story generation."""
        story = await generator.generate_with_template()
        
        # Check required fields
        required_fields = ['title', 'genre', 'setting', 'main_characters', 'plot_summary', 'mood', 'target_image_style']
        for field in required_fields:
            assert field in story
            assert story[field] is not None
        
        # Check data types
        assert isinstance(story['title'], str)
        assert isinstance(story['main_characters'], list)
        assert len(story['main_characters']) > 0
        assert len(story['plot_summary']) > 20  # Should be substantial
    
    @pytest.mark.asyncio
    async def test_random_generation(self, generator):
        """Test random story generation."""
        story = await generator.generate_random()
        
        # Check structure
        assert 'title' in story
        assert 'genre' in story
        assert 'plot_summary' in story
        assert len(story['plot_summary']) > 50  # Random stories should be detailed
    
    @pytest.mark.asyncio
    async def test_buddha_scene_generation(self, generator):
        """Test Buddha scene generation."""
        story = await generator.generate_buddha_scene()
        
        assert story['genre'] == 'spiritual'
        assert 'Buddha' in story['main_characters']
        assert 'title' in story
        assert story['target_image_style'] == 'spiritual art, golden tones, divine lighting'
    
    def test_image_prompt_generation(self, generator):
        """Test image prompt generation from story data."""
        sample_story = {
            'title': 'Test Story',
            'setting': 'ancient temple',
            'main_characters': ['hero', 'sage'],
            'mood': 'mystical',
            'target_image_style': 'fantasy art',
            'plot_summary': 'A hero seeks wisdom from an ancient sage in a mystical temple.'
        }
        
        prompt = generator.generate_image_prompt(sample_story)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 50
        assert 'ancient temple' in prompt
        assert 'mystical' in prompt
        assert 'fantasy art' in prompt
    
    def test_weighted_choice(self, generator):
        """Test weighted random choice functionality."""
        choices = ['a', 'b', 'c']
        weights = {'a': 0.5, 'b': 0.3, 'c': 0.2}
        
        # Test multiple selections to check distribution
        results = []
        for _ in range(100):
            choice = generator._weighted_choice(choices, weights)
            results.append(choice)
        
        # Should have selected all options
        assert 'a' in results
        assert 'b' in results
        assert 'c' in results
        
        # 'a' should be most common (though this is probabilistic)
        assert results.count('a') > results.count('c')
    
    @pytest.mark.asyncio
    async def test_story_validation(self, generator):
        """Test story generation includes validation."""
        story = await generator.generate_story(method='template')
        
        assert 'validation' in story
        assert 'content_safety' in story
        assert 'generation_method' in story
        assert story['generation_method'] == 'template'
    
    def test_export_functionality(self, generator):
        """Test story collection export."""
        # Create sample stories
        stories = [
            {
                'title': 'Story 1',
                'genre': 'fantasy',
                'plot_summary': 'A magical adventure begins.'
            },
            {
                'title': 'Story 2', 
                'genre': 'sci-fi',
                'plot_summary': 'Space exploration leads to discovery.'
            }
        ]
        
        # Test export
        filename = generator.export_story_collection(stories, 'test_collection.json')
        
        # Check file was created
        assert Path(filename).exists()
        
        # Clean up
        Path(filename).unlink()
    
    @pytest.mark.asyncio
    async def test_auto_method_selection(self, generator):
        """Test automatic method selection."""
        story = await generator.generate_story(method='auto')
        
        assert story is not None
        assert 'generation_method' in story
        # Should select template method when OpenAI is not available
        assert story['generation_method'] in ['template', 'openai']
    
    def test_generation_methods_mapping(self, generator):
        """Test all generation methods are properly mapped."""
        expected_methods = ['openai', 'template', 'interactive', 'random', 'buddha']
        
        for method in expected_methods:
            assert method in generator.generation_methods
            assert callable(generator.generation_methods[method])


class TestStoryValidation:
    """Test story validation functionality."""
    
    def test_valid_story_structure(self):
        """Test validation of valid story structure."""
        from utils.validators import validate_story_prompt
        
        valid_story = {
            'title': 'The Great Adventure',
            'genre': 'fantasy',
            'setting': 'A mystical forest filled with ancient magic',
            'main_characters': ['Brave Knight', 'Wise Wizard'],
            'plot_summary': 'In a land where magic flows like rivers, a brave knight and wise wizard must unite to defeat an ancient evil that threatens to consume their world. Their journey takes them through enchanted forests, across treacherous mountains, and into the heart of darkness itself.',
            'mood': 'epic',
            'target_image_style': 'fantasy art'
        }
        
        result = validate_story_prompt(valid_story)
        assert result['valid'] is True
        assert len(result['errors']) == 0
    
    def test_invalid_story_structure(self):
        """Test validation of invalid story structure."""
        from utils.validators import validate_story_prompt
        
        invalid_story = {
            'title': '',  # Empty title
            'plot_summary': 'Too short'  # Too short
        }
        
        result = validate_story_prompt(invalid_story)
        assert result['valid'] is False
        assert len(result['errors']) > 0


if __name__ == '__main__':
    pytest.main([__file__])
