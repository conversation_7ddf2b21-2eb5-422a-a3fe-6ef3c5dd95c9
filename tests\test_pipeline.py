"""
Integration Tests for Storytelling Pipeline

Tests for the complete pipeline orchestration and integration.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
import sys

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from core.pipeline import StorytellingPipeline
from config.settings import ConfigManager


class TestStorytellingPipeline:
    """Test cases for the complete storytelling pipeline."""
    
    @pytest.fixture
    def pipeline(self):
        """Create a pipeline instance for testing."""
        return StorytellingPipeline()
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create temporary output directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    def test_pipeline_initialization(self, pipeline):
        """Test pipeline initializes correctly."""
        assert pipeline is not None
        assert hasattr(pipeline, 'story_generator')
        assert hasattr(pipeline, 'image_generator')
        assert hasattr(pipeline, 'effects_processor')
        assert hasattr(pipeline, 'file_manager')
    
    def test_pipeline_status(self, pipeline):
        """Test pipeline status reporting."""
        status = pipeline.get_pipeline_status()
        
        required_keys = ['pipeline_initialized', 'current_session', 'progress', 'components', 'configuration']
        for key in required_keys:
            assert key in status
        
        assert status['pipeline_initialized'] is True
        assert 'story_generator' in status['components']
        assert 'image_generator' in status['components']
        assert 'effects_processor' in status['components']
    
    def test_pipeline_validation(self, pipeline):
        """Test pipeline setup validation."""
        validation = pipeline.validate_pipeline_setup()
        
        assert 'valid' in validation
        assert 'issues' in validation
        assert 'warnings' in validation
        assert 'recommendations' in validation
        
        # Should have warnings about missing API keys but still be functional
        assert isinstance(validation['warnings'], list)
        assert isinstance(validation['recommendations'], list)
    
    def test_auto_effect_selection(self, pipeline):
        """Test automatic effect selection based on story characteristics."""
        # Test fantasy story
        fantasy_story = {
            'genre': 'fantasy',
            'mood': 'epic'
        }
        effects = pipeline._auto_select_effects(fantasy_story)
        assert isinstance(effects, list)
        assert len(effects) > 0
        assert 'dramatic' in effects or 'hdr' in effects
        
        # Test sci-fi story
        scifi_story = {
            'genre': 'sci-fi',
            'mood': 'futuristic'
        }
        effects = pipeline._auto_select_effects(scifi_story)
        assert isinstance(effects, list)
        assert len(effects) > 0
    
    def test_progress_tracking(self, pipeline):
        """Test progress tracking functionality."""
        progress_updates = []
        
        def progress_callback(completed, total, task):
            progress_updates.append((completed, total, task))
        
        pipeline.set_progress_callback(progress_callback)
        
        # Simulate progress updates
        pipeline.total_steps = 4
        pipeline.completed_steps = 0
        
        pipeline._update_progress("Test task 1")
        pipeline._update_progress("Test task 2")
        
        assert len(progress_updates) == 2
        assert progress_updates[0] == (1, 4, "Test task 1")
        assert progress_updates[1] == (2, 4, "Test task 2")
    
    @pytest.mark.asyncio
    async def test_template_only_generation(self, pipeline):
        """Test generation using only template method (no API keys required)."""
        # This test should work without API keys
        try:
            result = await pipeline.generate_complete_story(
                story_method='template',
                image_service='auto',  # Should gracefully handle no API keys
                effects=['dramatic'],
                output_format='png'
            )
            
            # Should have story data even if image generation fails
            assert 'story_data' in result
            assert result['story_data'] is not None
            
            # Check story structure
            story = result['story_data']
            assert 'title' in story
            assert 'plot_summary' in story
            assert 'generation_method' in story
            assert story['generation_method'] == 'template'
            
        except Exception as e:
            # If it fails due to missing dependencies, that's expected
            assert "API" in str(e) or "client" in str(e).lower()
    
    def test_session_management(self, pipeline):
        """Test session directory management."""
        # Create session
        session_dir = pipeline.file_manager.create_session_directory()
        assert session_dir.exists()
        assert session_dir.is_dir()
        
        # Check subdirectories
        expected_subdirs = ['stories', 'images', 'processed', 'metadata']
        for subdir in expected_subdirs:
            assert (session_dir / subdir).exists()
    
    @pytest.mark.asyncio
    async def test_batch_generation_structure(self, pipeline):
        """Test batch generation structure (without actual API calls)."""
        # Mock the individual generation to avoid API calls
        original_method = pipeline.generate_complete_story
        
        async def mock_generation(*args, **kwargs):
            return {
                'success': True,
                'story_data': {'title': 'Mock Story', 'genre': 'test'},
                'session_directory': '/mock/path',
                'errors': []
            }
        
        pipeline.generate_complete_story = mock_generation
        
        try:
            result = await pipeline.generate_batch_stories(
                count=2,
                story_method='template'
            )
            
            assert 'success' in result
            assert 'total_requested' in result
            assert 'successful_generations' in result
            assert 'stories' in result
            assert result['total_requested'] == 2
            
        finally:
            # Restore original method
            pipeline.generate_complete_story = original_method
    
    def test_export_functionality(self, pipeline, temp_output_dir):
        """Test session export functionality."""
        # Create a mock session directory
        session_dir = Path(temp_output_dir) / "test_session"
        session_dir.mkdir()
        
        # Create some mock files
        (session_dir / "stories").mkdir()
        (session_dir / "images").mkdir()
        (session_dir / "stories" / "story1.json").write_text('{"title": "Test Story"}')
        (session_dir / "images" / "image1.png").write_text("mock image data")
        
        # Test JSON export
        export_path = pipeline.export_session_data(
            session_path=str(session_dir),
            export_format='json'
        )
        
        assert Path(export_path).exists()
        assert export_path.endswith('.json')
        
        # Test ZIP export
        export_path_zip = pipeline.export_session_data(
            session_path=str(session_dir),
            export_format='zip'
        )
        
        assert Path(export_path_zip).exists()
        assert export_path_zip.endswith('.zip')


class TestPipelineErrorHandling:
    """Test error handling in the pipeline."""
    
    @pytest.fixture
    def pipeline(self):
        return StorytellingPipeline()
    
    def test_invalid_story_method(self, pipeline):
        """Test handling of invalid story generation method."""
        with pytest.raises(ValueError):
            asyncio.run(pipeline.story_generator.generate_story(method='invalid_method'))
    
    def test_missing_session_export(self, pipeline):
        """Test export with missing session."""
        with pytest.raises(ValueError):
            pipeline.export_session_data(session_path=None)
    
    def test_nonexistent_session_export(self, pipeline):
        """Test export with nonexistent session path."""
        with pytest.raises(FileNotFoundError):
            pipeline.export_session_data(session_path='/nonexistent/path')


class TestConfigurationIntegration:
    """Test configuration integration with pipeline."""
    
    def test_config_loading(self):
        """Test configuration loading in pipeline."""
        pipeline = StorytellingPipeline()
        
        assert pipeline.config is not None
        assert hasattr(pipeline.config, 'get_story_config')
        assert hasattr(pipeline.config, 'get_image_config')
        assert hasattr(pipeline.config, 'get_effects_config')
    
    def test_custom_config_path(self, tmp_path):
        """Test pipeline with custom config path."""
        # Create a temporary config file
        config_file = tmp_path / "test_config.yaml"
        config_content = """
story_generation:
  default_method: "template"
image_generation:
  primary_service: "openai"
effects_processing:
  default_intensity: "high"
pipeline:
  output_structure:
    base_dir: "test_output"
"""
        config_file.write_text(config_content)
        
        # Initialize pipeline with custom config
        pipeline = StorytellingPipeline(str(config_file))
        
        # Verify config is loaded
        assert pipeline.config.get('story_generation.default_method') == 'template'
        assert pipeline.config.get('effects_processing.default_intensity') == 'high'


if __name__ == '__main__':
    pytest.main([__file__])
