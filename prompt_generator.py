import json
import csv

class BuddhaStoryPromptGenerator:
    def __init__(self):
        self.effects = {
            "lighting": [
                "dramatic lighting", "soft shadows", "sun rays through trees",
                "candle-lit glow", "golden hour light", "moonlight illumination"
            ],
            "textures": ["ultra-detailed textures", "aged stone", "smooth silk robes"],
            "depth": ["soft depth of field", "bokeh background"],
            "atmosphere": ["volumetric fog", "ambient particles", "floating incense smoke"],
            "lens": ["lens flare", "chromatic aberration", "anamorphic lens effects"],
            "motion": ["motion blur", "fluttering leaves", "ripples on water"],
            "color": ["rich color grading", "golden tones", "spiritual aura glow"],
            "realism": ["photorealistic", "Ray-Traced reflections", "Unreal Engine 5 style"]
        }

        self.buddha_scenes = [
            {
                "title": "Birth of Siddhartha",
                "scene": "Queen <PERSON> holding newborn <PERSON><PERSON><PERSON><PERSON> as heavenly beings shower flowers",
                "location": "Lumbini Garden",
                "time": "morning",
                "mood": "divine"
            },
            {
                "title": "The Four Sights",
                "scene": "Prince <PERSON> witnessing an old man, a sick man, a dead body, and a monk",
                "location": "streets of Kapilavastu",
                "time": "afternoon",
                "mood": "transformative"
            },
            {
                "title": "Renunciation",
                "scene": "Siddhartha leaving the palace on horseback at night",
                "location": "Kapilavastu outskirts",
                "time": "midnight",
                "mood": "solemn"
            },
            {
                "title": "Meditation under Bodhi Tree",
                "scene": "Siddhartha meditating under the Bodhi tree as stars shimmer",
                "location": "Bodh Gaya",
                "time": "early dawn",
                "mood": "spiritual"
            },
            {
                "title": "Enlightenment",
                "scene": "Buddha glowing with radiant light as he attains enlightenment",
                "location": "under the Bodhi tree",
                "time": "sunrise",
                "mood": "transcendent"
            },
            {
                "title": "First Sermon at Sarnath",
                "scene": "Buddha teaching the Dharma to his first disciples",
                "location": "Deer Park, Sarnath",
                "time": "late afternoon",
                "mood": "inspiring"
            },
            {
                "title": "Parinirvana (Final Passing)",
                "scene": "Buddha lying peacefully between sal trees surrounded by monks",
                "location": "Kushinagar",
                "time": "twilight",
                "mood": "peaceful"
            }
        ]

    def _generate_effects(self):
        return ", ".join([
            self.effects["lighting"][0],
            self.effects["textures"][0],
            self.effects["depth"][0],
            self.effects["atmosphere"][0],
            self.effects["lens"][0],
            self.effects["motion"][0],
            self.effects["color"][0],
            self.effects["color"][1],
            self.effects["realism"][0],
            self.effects["realism"][1],
        ])

    def generate_prompt(self, scene_description, location, time_of_day, mood, style="cinematic"):
        effects_prompt = self._generate_effects()
        return (
            f"A {style} scene of {scene_description}, set in {location} during {time_of_day}, "
            f"mood is {mood}. Visual effects include: {effects_prompt}. "
            f"Rendered in 8K, photorealistic, Unreal Engine 5 style, trending on ArtStation."
        )

    def generate_all_scenes(self):
        all_prompts = []
        for scene in self.buddha_scenes:
            prompt = self.generate_prompt(
                scene_description=scene["scene"],
                location=scene["location"],
                time_of_day=scene["time"],
                mood=scene["mood"]
            )
            all_prompts.append({
                "title": scene["title"],
                "scene": scene["scene"],
                "location": scene["location"],
                "time": scene["time"],
                "mood": scene["mood"],
                "prompt": prompt
            })
        return all_prompts

    def export_to_json(self, filename="buddha_story_prompts.json"):
        data = self.generate_all_scenes()
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"✅ Exported to {filename}")

    def export_to_csv(self, filename="buddha_story_prompts.csv"):
        data = self.generate_all_scenes()
        with open(filename, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        print(f"✅ Exported to {filename}")
