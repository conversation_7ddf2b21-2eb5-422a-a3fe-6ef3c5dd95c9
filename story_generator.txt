import cv2
import numpy as np
from moviepy.editor import (
    ImageClip, CompositeVideoClip, concatenate_videoclips,
    AudioFileClip
)
from PIL import Image, ImageEnhance
import os

class BuddhaStoryVideoGenerator:
    def __init__(self, image_paths, output_path="buddha_story_video.mp4", duration_per_scene=8, audio_path=None):
        self.image_paths = image_paths
        self.output_path = output_path
        self.duration = duration_per_scene
        self.audio_path = audio_path

    def apply_effects(self, image_path):
        base_image = cv2.imread(image_path)
        img = Image.fromarray(base_image)

        # Enhance contrast and color
        img = ImageEnhance.Contrast(img).enhance(1.25)
        img = ImageEnhance.Color(img).enhance(1.15)

        image_np = np.array(img)

        # Add soft particles and ambient light
        noise = np.random.randint(0, 40, image_np.shape, dtype='uint8')
        image_np = cv2.addWeighted(image_np, 0.98, noise, 0.02, 0)

        glare = np.zeros_like(image_np)
        cv2.circle(glare, (image_np.shape[1]//2, image_np.shape[0]//3), 180, (255, 255, 220), -1)
        image_np = cv2.addWeighted(image_np, 1.0, glare, 0.07, 0)

        # Save temp image with effects
        temp_path = f"temp_effects_{os.path.basename(image_path)}"
        cv2.imwrite(temp_path, image_np)
        return temp_path

    def generate_scene_clip(self, image_path):
        img_fx_path = self.apply_effects(image_path)
        clip = ImageClip(img_fx_path).set_duration(self.duration).resize(width=1080)

        # Ken Burns effect (zoom-in)
        clip = clip.fx(lambda c: c.zoom(1.1))

        return clip

    def generate_full_video(self):
        print("🎞️ Generating video clips...")
        clips = [self.generate_scene_clip(path) for path in self.image_paths]

        print("🎬 Concatenating all clips...")
        final_video = concatenate_videoclips(clips, method="compose")

        # Add audio if provided
        if self.audio_path and os.path.exists(self.audio_path):
            audio = AudioFileClip(self.audio_path).set_duration(final_video.duration)
            final_video = final_video.set_audio(audio)

        print("💾 Writing final video...")
        final_video.write_videofile(self.output_path, fps=24)

        # Cleanup temp images
        for clip_path in self.image_paths:
            temp_path = f"temp_effects_{os.path.basename(clip_path)}"
            if os.path.exists(temp_path):
                os.remove(temp_path)

        print(f"✅ Video saved: {self.output_path}")
