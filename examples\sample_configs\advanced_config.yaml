# Advanced Configuration for Storytelling Pipeline
# This configuration includes all available options and optimizations

# Story Generation Settings
story_generation:
  default_method: "openai"  # Use AI for best quality
  
  # OpenAI GPT settings
  openai:
    model: "gpt-4"  # Use GPT-4 for best results
    max_tokens: 800  # Longer stories
    temperature: 0.9  # High creativity
    system_prompt: "You are a master storyteller who creates vivid, cinematic narratives perfect for visual adaptation. Focus on rich descriptions, compelling characters, and scenes that would make stunning images."
  
  # Extended template options
  template:
    genres: ["epic fantasy", "space opera", "cyberpunk", "steampunk", "gothic horror", "romantic adventure", "mythological", "post-apocalyptic"]
    settings: [
      "floating crystal city", "underwater palace", "space station orbiting a black hole",
      "steampunk London", "cyberpunk Tokyo", "ancient Egyptian temple", "Norse Valhalla",
      "enchanted library", "dragon's lair", "time traveler's laboratory"
    ]
    characters: [
      "time-traveling archaeologist", "cybernetic samurai", "dragon whisperer", 
      "quantum physicist", "steampunk inventor", "mythical shapeshifter",
      "AI consciousness", "interdimensional explorer", "ancient god reborn"
    ]
    conflicts: [
      "preventing timeline collapse", "awakening ancient powers", "bridging two worlds",
      "solving cosmic mysteries", "breaking ancient curses", "saving dying civilizations",
      "mastering forbidden magic", "uniting warring dimensions"
    ]
  
  # Strict validation for high-quality output
  validation:
    min_length: 100
    max_length: 2000
    content_filter: true
    require_visual_elements: true

# Image Generation Settings
image_generation:
  primary_service: "openai"
  fallback_service: "stability"
  
  # High-quality OpenAI settings
  openai:
    model: "dall-e-3"
    size: "1024x1024"  # Can use "1792x1024" or "1024x1792" for different aspects
    quality: "hd"  # High definition
    style: "vivid"  # More dramatic and hyper-real
  
  # Stable Diffusion settings for fallback
  stability:
    model: "stable-diffusion-xl-1024-v1-0"
    steps: 50  # Higher steps for better quality
    cfg_scale: 8.0  # Strong prompt adherence
  
  # Advanced prompt enhancement
  prompt_enhancement:
    style_keywords: [
      "cinematic masterpiece", "8K ultra-detailed", "award-winning photography",
      "trending on ArtStation", "Unreal Engine 5", "ray-traced lighting"
    ]
    lighting_keywords: [
      "volumetric lighting", "golden hour cinematography", "dramatic chiaroscuro",
      "ethereal rim lighting", "atmospheric perspective", "god rays"
    ]
    composition_keywords: [
      "rule of thirds", "dynamic composition", "perfect framing",
      "shallow depth of field", "bokeh background", "leading lines"
    ]
    quality_keywords: [
      "museum quality", "gallery exhibition", "professional grade",
      "IMAX quality", "fine art photography", "technical perfection"
    ]

# Advanced Visual Effects Settings
effects_processing:
  default_intensity: "high"  # Maximum quality
  
  # Extended color effects
  color_effects:
    cinematic: {contrast: 1.4, saturation: 1.3, brightness: 1.05, color_grading: true}
    vintage_film: {contrast: 1.2, saturation: 0.9, brightness: 1.1, film_grain: true}
    neon_cyberpunk: {contrast: 1.6, saturation: 2.0, brightness: 0.95, neon_glow: true}
    golden_hour: {contrast: 1.1, saturation: 1.4, brightness: 1.2, warm_tones: true}
  
  # Advanced artistic effects
  artistic_effects:
    renaissance_painting: {oil_painting: true, classical_lighting: true, rich_textures: true}
    anime_style: {cell_shading: true, vibrant_colors: true, sharp_lines: true}
    concept_art: {digital_painting: true, atmospheric: true, detailed_rendering: true}
    photorealistic: {ray_tracing: true, subsurface_scattering: true, perfect_lighting: true}
  
  # Motion effects for video
  motion_effects:
    epic_ken_burns: {zoom_factor: 1.15, pan_speed: "slow", easing: "smooth"}
    dramatic_zoom: {zoom_factor: 1.3, focus_point: "center", duration: 10}
    parallax_3d: {layers: 5, depth_factor: 0.2, motion_blur: true}
  
  # High-quality output settings
  output_formats:
    static: ["png"]  # PNG for maximum quality
    animated: ["mp4"]  # MP4 for best video quality
    video_settings:
      fps: 30  # Smooth motion
      bitrate: "10M"  # High bitrate for quality
      codec: "libx264"
      preset: "slow"  # Better compression

# Advanced Pipeline Settings
pipeline:
  output_structure:
    base_dir: "output"
    timestamp_format: "%Y%m%d_%H%M%S"
    subdirs: ["stories", "images", "processed", "metadata", "previews", "exports"]
    create_thumbnails: true
    organize_by_date: true
  
  logging:
    level: "DEBUG"  # Detailed logging
    format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    file: "storytelling_pipeline.log"
    max_size_mb: 50
    backup_count: 10
    separate_error_log: true
  
  error_handling:
    retry_attempts: 5  # More retries
    retry_delay: 3
    exponential_backoff: true
    graceful_degradation: true
    detailed_error_reporting: true
  
  performance:
    batch_size: 8  # Larger batches for efficiency
    concurrent_requests: 5  # More concurrent processing
    cache_enabled: true
    cache_ttl_hours: 48  # Longer cache
    memory_optimization: true
    gpu_acceleration: true  # If available
  
  quality_control:
    image_quality_threshold: 0.8  # High quality threshold
    auto_retry_low_quality: true
    save_generation_metadata: true
    create_quality_reports: true

# Advanced CLI Settings
cli:
  default_batch_size: 5
  progress_bar: true
  verbose: true
  interactive_prompts: true
  auto_open_results: false  # Set to true to auto-open generated content
  export_formats: ["json", "csv", "html"]
  
  # Custom presets for quick access
  presets:
    fantasy_epic:
      story_method: "openai"
      genre: "epic fantasy"
      effects: ["cinematic", "dramatic"]
      format: "png"
    
    scifi_sequence:
      story_method: "template"
      genre: "space opera"
      effects: ["neon_cyberpunk", "concept_art"]
      format: "mp4"
    
    artistic_collection:
      story_method: "random"
      effects: ["renaissance_painting", "golden_hour"]
      format: "png"
      batch_size: 10
