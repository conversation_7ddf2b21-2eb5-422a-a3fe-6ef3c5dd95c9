import os
import requests
import time
from typing import Optional, Dict, Any
from pydantic import BaseSettings, BaseModel, HttpUrl, validator
from datetime import datetime
import logging
from pathlib import Path
from urllib.parse import urljoin

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('imagine_art_client.log')
    ]
)
logger = logging.getLogger(__name__)


class ImagineArtConfig(BaseSettings):
    """Configuration for Imagine.Art API client."""
    api_key: str
    base_url: HttpUrl = "https://api.imagine.art/v1/"
    timeout: int = 60  # Longer timeout for image generation
    max_retries: int = 3
    retry_delay: float = 5.0
    default_width: int = 1024
    default_height: int = 1024
    save_directory: str = "generated_images"
    polling_interval: int = 5  # Seconds between status checks
    max_polling_time: int = 300  # Max 5 minutes for generation

    class Config:
        env_file = ".env"
        env_prefix = "IMAGINE_ART_"


class ImageGenerationRequest(BaseModel):
    """Model for image generation request payload."""
    prompt: str
    width: Optional[int] = None
    height: Optional[int] = None
    style: Optional[str] = None
    negative_prompt: Optional[str] = None
    seed: Optional[int] = None
    steps: Optional[int] = None
    cfg_scale: Optional[float] = None
    sampler: Optional[str] = None
    model: Optional[str] = None

    @validator('width', 'height')
    def validate_dimensions(cls, v):
        if v is not None and (v < 256 or v > 2048):
            raise ValueError("Dimensions must be between 256 and 2048")
        return v

    @validator('steps')
    def validate_steps(cls, v):
        if v is not None and (v < 10 or v > 150):
            raise ValueError("Steps must be between 10 and 150")
        return v


class ImagineArtClient:
    """Professional Imagine.Art API client for image generation."""

    def __init__(self, config: ImagineArtConfig):
        self.config = config
        self.session = requests.Session()
        self._configure_session()
        self._ensure_save_directory()

    def _ensure_save_directory(self):
        """Ensure the save directory exists."""
        Path(self.config.save_directory).mkdir(parents=True, exist_ok=True)

    def _configure_session(self):
        """Configure the requests session."""
        self.session.headers.update({
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "ImagineArtPythonClient/1.0.0"
        })
        self.session.timeout = self.config.timeout

    def generate_image(
        self,
        request: ImageGenerationRequest,
        save_to_file: bool = True
    ) -> Optional[bytes]:
        """
        Generate an image from a prompt using Imagine.Art API.

        Args:
            request: Image generation request parameters
            save_to_file: Whether to save the image to disk

        Returns:
            bytes: The generated image data if successful, None otherwise
        """
        # Step 1: Submit generation task
        task_id = self._submit_generation_task(request)
        if not task_id:
            return None

        # Step 2: Poll for task completion
        image_url = self._poll_for_completion(task_id)
        if not image_url:
            return None

        # Step 3: Download the generated image
        image_data = self._download_image(image_url)
        if not image_data:
            return None

        # Step 4: Save if requested
        if save_to_file and image_data:
            self._save_image(image_data, request.prompt)

        return image_data

    def _submit_generation_task(self, request: ImageGenerationRequest) -> Optional[str]:
        """Submit image generation task to API."""
        endpoint = urljoin(str(self.config.base_url), "generate")
        payload = self._build_payload(request)

        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(
                    endpoint,
                    json=payload,
                    timeout=self.config.timeout
                )
                response.raise_for_status()
                data = response.json()

                if not data.get('success', False):
                    logger.error(f"API reported failure: {data.get('message', 'Unknown error')}")
                    return None

                return data.get('task_id')

            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay)
                else:
                    logger.error(f"Failed after {self.config.max_retries} attempts: {str(e)}")
                    return None

    def _poll_for_completion(self, task_id: str) -> Optional[str]:
        """Poll API for task completion and return image URL when ready."""
        endpoint = urljoin(str(self.config.base_url), f"task/status/{task_id}")
        start_time = time.time()

        while time.time() - start_time < self.config.max_polling_time:
            try:
                response = self.session.get(endpoint, timeout=self.config.timeout)
                response.raise_for_status()
                data = response.json()

                status = data.get('status')
                if status == 'completed':
                    return data.get('image_url')
                elif status in ('failed', 'cancelled'):
                    logger.error(f"Task failed with status: {status}")
                    return None

                # Still processing
                time.sleep(self.config.polling_interval)

            except requests.exceptions.RequestException as e:
                logger.error(f"Error polling task status: {str(e)}")
                return None

        logger.error("Max polling time exceeded")
        return None

    def _download_image(self, image_url: str) -> Optional[bytes]:
        """Download generated image from URL."""
        try:
            response = self.session.get(image_url, timeout=self.config.timeout)
            response.raise_for_status()
            return response.content
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading image: {str(e)}")
            return None

    def _build_payload(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """Construct the API request payload."""
        payload = {
            "prompt": request.prompt,
            "width": request.width or self.config.default_width,
            "height": request.height or self.config.default_height,
        }

        if request.style:
            payload["style"] = request.style
        if request.negative_prompt:
            payload["negative_prompt"] = request.negative_prompt
        if request.seed is not None:
            payload["seed"] = request.seed
        if request.steps:
            payload["steps"] = request.steps
        if request.cfg_scale:
            payload["cfg_scale"] = request.cfg_scale
        if request.sampler:
            payload["sampler"] = request.sampler
        if request.model:
            payload["model"] = request.model

        return payload

    def _save_image(self, image_data: bytes, prompt: str) -> str:
        """Save image data to a file with a generated name."""
        safe_prompt = "".join(
            c if c.isalnum() or c in (' ', '-', '_') else '_'
            for c in prompt
        )[:50]

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{safe_prompt}.png"
        filepath = Path(self.config.save_directory) / filename

        with open(filepath, "wb") as f:
            f.write(image_data)

        logger.info(f"Image saved to {filepath}")
        return str(filepath)


def example_usage():
    """Example usage of the Imagine.Art client."""
    try:
        config = ImagineArtConfig()
        client = ImagineArtClient(config)

        request = ImageGenerationRequest(
            prompt="A majestic lion standing on a cliff at sunset, digital art, highly detailed, 8k",
            width=1024,
            height=768,
            style="fantasy_art",
            negative_prompt="blurry, low quality, distorted",
            steps=50,
            cfg_scale=7.5,
            model="realistic_vision"
        )

        image_data = client.generate_image(request)

        if image_data:
            print("Image generated successfully!")
            # You can now use image_data (bytes) as needed

    except Exception as e:
        logger.error(f"Error in example usage: {str(e)}")


if __name__ == "__main__":
    example_usage()