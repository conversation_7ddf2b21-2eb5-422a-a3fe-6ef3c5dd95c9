"""
Enhanced Story Prompt Generator

Extends the existing BuddhaStoryPromptGenerator with comprehensive story generation capabilities
including OpenAI GPT integration, template-based generation, interactive prompts, and random generation.
"""

import json
import random
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import sys
import os

# Add parent directory to path to import existing prompt_generator
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt_generator import Buddha<PERSON>toryPromptGenerator

from config.settings import load_config
from utils.api_client import OpenAIClient
from utils.validators import validate_story_prompt, validate_content_safety, sanitize_filename

logger = logging.getLogger(__name__)


class StoryPromptGenerator(BuddhaStoryPromptGenerator):
    """Enhanced story prompt generator with multiple generation methods."""
    
    def __init__(self, config_manager=None):
        """Initialize the enhanced story generator.
        
        Args:
            config_manager: Configuration manager instance
        """
        # Initialize parent class
        super().__init__()
        
        # Load configuration
        self.config = config_manager or load_config()
        self.story_config = self.config.get_story_config()
        
        # Initialize API clients
        self.openai_client = None
        self._init_api_clients()
        
        # Load template data
        self.template_data = self._load_template_data()
        
        # Generation method mapping
        self.generation_methods = {
            "openai": self.generate_with_openai,
            "template": self.generate_with_template,
            "interactive": self.generate_interactive,
            "random": self.generate_random,
            "buddha": self.generate_buddha_scene  # Original Buddha scenes
        }
    
    def _init_api_clients(self):
        """Initialize API clients if keys are available."""
        api_keys = self.config.get_api_keys()
        
        if api_keys.get('openai'):
            try:
                self.openai_client = OpenAIClient(
                    api_key=api_keys['openai'],
                    organization_id=api_keys.get('openai_org_id')
                )
                logger.info("OpenAI client initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
    
    def _load_template_data(self) -> Dict[str, List[str]]:
        """Load template data for story generation."""
        template_config = self.story_config.get('template', {})
        
        default_templates = {
            "genres": ["fantasy", "sci-fi", "mystery", "romance", "adventure", "horror", "drama"],
            "settings": [
                "ancient temple", "futuristic city", "enchanted forest", "space station",
                "medieval castle", "underwater realm", "floating islands", "cyberpunk metropolis",
                "mystical library", "abandoned laboratory", "crystal caves", "sky palace"
            ],
            "characters": [
                "wise sage", "brave warrior", "mysterious stranger", "young apprentice",
                "ancient guardian", "cosmic entity", "rebel leader", "time traveler",
                "shapeshifter", "oracle", "inventor", "explorer"
            ],
            "conflicts": [
                "quest for truth", "battle against darkness", "journey of discovery",
                "test of courage", "search for redemption", "fight for survival",
                "race against time", "forbidden love", "ancient prophecy", "lost memories"
            ],
            "moods": [
                "epic", "mysterious", "romantic", "dark", "hopeful", "melancholic",
                "triumphant", "eerie", "peaceful", "intense", "whimsical", "dramatic"
            ],
            "visual_styles": [
                "cinematic", "painterly", "photorealistic", "fantasy art", "concept art",
                "digital painting", "oil painting", "watercolor", "anime style", "gothic"
            ]
        }
        
        # Merge with config templates
        for key, default_list in default_templates.items():
            if key in template_config:
                default_templates[key].extend(template_config[key])
                # Remove duplicates while preserving order
                default_templates[key] = list(dict.fromkeys(default_templates[key]))
        
        return default_templates
    
    async def generate_story(self, method: str = "auto", prompt: Optional[str] = None, 
                           **kwargs) -> Dict[str, Any]:
        """Generate a story using the specified method.
        
        Args:
            method: Generation method (openai, template, interactive, random, buddha, auto)
            prompt: Custom prompt for generation
            **kwargs: Additional parameters for specific methods
            
        Returns:
            Generated story data
        """
        # Auto-select method based on available resources
        if method == "auto":
            method = self._select_best_method()
        
        if method not in self.generation_methods:
            raise ValueError(f"Unknown generation method: {method}")
        
        logger.info(f"Generating story using method: {method}")
        
        try:
            # Generate story
            story_data = await self.generation_methods[method](prompt=prompt, **kwargs)
            
            # Validate generated story
            validation_result = validate_story_prompt(story_data)
            story_data["validation"] = validation_result
            
            # Content safety check
            if "plot_summary" in story_data:
                safety_result = validate_content_safety(story_data["plot_summary"])
                story_data["content_safety"] = safety_result
            
            # Add metadata
            story_data.update({
                "generation_method": method,
                "generator_version": "1.0.0",
                "timestamp": asyncio.get_event_loop().time()
            })
            
            logger.info(f"Story generated successfully using {method}")
            return story_data
            
        except Exception as e:
            logger.error(f"Story generation failed with method {method}: {e}")
            raise
    
    def _select_best_method(self) -> str:
        """Select the best available generation method."""
        if self.openai_client:
            return "openai"
        else:
            return "template"
    
    async def generate_with_openai(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story using OpenAI GPT.
        
        Args:
            prompt: Custom generation prompt
            **kwargs: Additional OpenAI parameters
            
        Returns:
            Generated story data
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not available")
        
        # Prepare generation prompt
        if prompt is None:
            prompt = self._create_openai_prompt(**kwargs)
        
        # Get OpenAI configuration
        openai_config = self.story_config.get('openai', {})
        
        # Generate story
        result = await self.openai_client.generate_story(
            prompt=prompt,
            model=openai_config.get('model', 'gpt-4'),
            max_tokens=openai_config.get('max_tokens', 500),
            temperature=openai_config.get('temperature', 0.8)
        )
        
        if not result.get('success'):
            raise Exception(f"OpenAI generation failed: {result.get('error')}")
        
        # Parse and structure the generated story
        story_text = result['story']
        structured_story = self._parse_openai_response(story_text)
        
        # Add generation metadata
        structured_story.update({
            "raw_response": story_text,
            "model_used": result.get('model'),
            "tokens_used": result.get('tokens_used')
        })
        
        return structured_story
    
    def _create_openai_prompt(self, **kwargs) -> str:
        """Create a prompt for OpenAI story generation."""
        genre = kwargs.get('genre') or random.choice(self.template_data['genres'])
        setting = kwargs.get('setting') or random.choice(self.template_data['settings'])
        mood = kwargs.get('mood') or random.choice(self.template_data['moods'])
        
        prompt = f"""Create a detailed story prompt for a {genre} story set in {setting} with a {mood} mood.

Please provide the story in the following JSON format:
{{
    "title": "Story title",
    "genre": "{genre}",
    "setting": "Detailed setting description",
    "main_characters": ["character1", "character2"],
    "plot_summary": "Detailed plot summary (100-300 words)",
    "mood": "{mood}",
    "target_image_style": "Visual style description for image generation"
}}

Make the story vivid and suitable for visual representation. Include specific details about characters, locations, and key scenes that would translate well to images."""
        
        return prompt
    
    def _parse_openai_response(self, response_text: str) -> Dict[str, Any]:
        """Parse OpenAI response into structured story data."""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                story_data = json.loads(json_match.group())
                return story_data
        except:
            pass
        
        # Fallback: create structure from text
        return {
            "title": "Generated Story",
            "genre": "unknown",
            "setting": "Generated setting",
            "main_characters": ["Generated character"],
            "plot_summary": response_text,
            "mood": "unknown",
            "target_image_style": "cinematic"
        }
    
    async def generate_with_template(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story using template-based approach.
        
        Args:
            prompt: Custom prompt (ignored for template method)
            **kwargs: Template parameters
            
        Returns:
            Generated story data
        """
        # Select random elements or use provided ones
        genre = kwargs.get('genre') or random.choice(self.template_data['genres'])
        setting = kwargs.get('setting') or random.choice(self.template_data['settings'])
        characters = kwargs.get('characters') or random.sample(
            self.template_data['characters'], 
            random.randint(1, 3)
        )
        conflict = kwargs.get('conflict') or random.choice(self.template_data['conflicts'])
        mood = kwargs.get('mood') or random.choice(self.template_data['moods'])
        visual_style = kwargs.get('visual_style') or random.choice(self.template_data['visual_styles'])
        
        # Generate title
        title = f"The {random.choice(['Chronicles', 'Legend', 'Tale', 'Saga', 'Quest'])} of {characters[0].title()}"
        
        # Generate plot summary
        plot_templates = [
            f"In the {setting}, {characters[0]} embarks on a {conflict}. Joined by {', '.join(characters[1:]) if len(characters) > 1 else 'loyal companions'}, they must overcome great challenges in this {mood} adventure.",
            f"When darkness threatens the {setting}, {characters[0]} discovers they are the key to a {conflict}. With the help of {', '.join(characters[1:]) if len(characters) > 1 else 'unexpected allies'}, they face their destiny in this {mood} tale.",
            f"The {setting} holds ancient secrets that {characters[0]} must uncover in their {conflict}. Accompanied by {', '.join(characters[1:]) if len(characters) > 1 else 'faithful friends'}, they navigate a {mood} journey of discovery."
        ]
        
        plot_summary = random.choice(plot_templates)
        
        return {
            "title": title,
            "genre": genre,
            "setting": setting,
            "main_characters": characters,
            "plot_summary": plot_summary,
            "mood": mood,
            "target_image_style": visual_style
        }
