"""
Enhanced Story Prompt Generator

Extends the existing BuddhaStoryPromptGenerator with comprehensive story generation capabilities
including OpenAI GPT integration, template-based generation, interactive prompts, and random generation.
"""

import json
import random
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import sys
import os

# Add parent directory to path to import existing prompt_generator
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt_generator import Buddha<PERSON>toryPromptGenerator

from config.settings import load_config
from utils.api_client import OpenAIClient
from utils.validators import validate_story_prompt, validate_content_safety, sanitize_filename

logger = logging.getLogger(__name__)


class StoryPromptGenerator(BuddhaStoryPromptGenerator):
    """Enhanced story prompt generator with multiple generation methods."""

    def __init__(self, config_manager=None):
        """Initialize the enhanced story generator.

        Args:
            config_manager: Configuration manager instance
        """
        # Initialize parent class
        super().__init__()

        # Load configuration
        self.config = config_manager or load_config()
        self.story_config = self.config.get_story_config()

        # Initialize API clients
        self.openai_client = None
        self._init_api_clients()

        # Load template data
        self.template_data = self._load_template_data()

        # Generation method mapping
        self.generation_methods = {
            "openai": self.generate_with_openai,
            "template": self.generate_with_template,
            "interactive": self.generate_interactive,
            "random": self.generate_random,
            "buddha": self.generate_buddha_scene  # Original Buddha scenes
        }

    def _init_api_clients(self):
        """Initialize API clients if keys are available."""
        api_keys = self.config.get_api_keys()

        if api_keys.get('openai'):
            try:
                self.openai_client = OpenAIClient(
                    api_key=api_keys['openai'],
                    organization_id=api_keys.get('openai_org_id')
                )
                logger.info("OpenAI client initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")

    def _load_template_data(self) -> Dict[str, List[str]]:
        """Load template data for story generation."""
        template_config = self.story_config.get('template', {})

        default_templates = {
            "genres": ["fantasy", "sci-fi", "mystery", "romance", "adventure", "horror", "drama"],
            "settings": [
                "ancient temple", "futuristic city", "enchanted forest", "space station",
                "medieval castle", "underwater realm", "floating islands", "cyberpunk metropolis",
                "mystical library", "abandoned laboratory", "crystal caves", "sky palace"
            ],
            "characters": [
                "wise sage", "brave warrior", "mysterious stranger", "young apprentice",
                "ancient guardian", "cosmic entity", "rebel leader", "time traveler",
                "shapeshifter", "oracle", "inventor", "explorer"
            ],
            "conflicts": [
                "quest for truth", "battle against darkness", "journey of discovery",
                "test of courage", "search for redemption", "fight for survival",
                "race against time", "forbidden love", "ancient prophecy", "lost memories"
            ],
            "moods": [
                "epic", "mysterious", "romantic", "dark", "hopeful", "melancholic",
                "triumphant", "eerie", "peaceful", "intense", "whimsical", "dramatic"
            ],
            "visual_styles": [
                "cinematic", "painterly", "photorealistic", "fantasy art", "concept art",
                "digital painting", "oil painting", "watercolor", "anime style", "gothic"
            ]
        }

        # Merge with config templates
        for key, default_list in default_templates.items():
            if key in template_config:
                default_templates[key].extend(template_config[key])
                # Remove duplicates while preserving order
                default_templates[key] = list(dict.fromkeys(default_templates[key]))

        return default_templates

    async def generate_story(self, method: str = "auto", prompt: Optional[str] = None,
                           **kwargs) -> Dict[str, Any]:
        """Generate a story using the specified method.

        Args:
            method: Generation method (openai, template, interactive, random, buddha, auto)
            prompt: Custom prompt for generation
            **kwargs: Additional parameters for specific methods

        Returns:
            Generated story data
        """
        # Auto-select method based on available resources
        if method == "auto":
            method = self._select_best_method()

        if method not in self.generation_methods:
            raise ValueError(f"Unknown generation method: {method}")

        logger.info(f"Generating story using method: {method}")

        try:
            # Generate story
            story_data = await self.generation_methods[method](prompt=prompt, **kwargs)

            # Validate generated story
            validation_result = validate_story_prompt(story_data)
            story_data["validation"] = validation_result

            # Content safety check
            if "plot_summary" in story_data:
                safety_result = validate_content_safety(story_data["plot_summary"])
                story_data["content_safety"] = safety_result

            # Add metadata
            story_data.update({
                "generation_method": method,
                "generator_version": "1.0.0",
                "timestamp": asyncio.get_event_loop().time()
            })

            logger.info(f"Story generated successfully using {method}")
            return story_data

        except Exception as e:
            logger.error(f"Story generation failed with method {method}: {e}")
            raise

    def _select_best_method(self) -> str:
        """Select the best available generation method."""
        if self.openai_client:
            return "openai"
        else:
            return "template"

    async def generate_with_openai(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story using OpenAI GPT.

        Args:
            prompt: Custom generation prompt
            **kwargs: Additional OpenAI parameters

        Returns:
            Generated story data
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not available")

        # Prepare generation prompt
        if prompt is None:
            prompt = self._create_openai_prompt(**kwargs)

        # Get OpenAI configuration
        openai_config = self.story_config.get('openai', {})

        # Generate story
        result = await self.openai_client.generate_story(
            prompt=prompt,
            model=openai_config.get('model', 'gpt-4'),
            max_tokens=openai_config.get('max_tokens', 500),
            temperature=openai_config.get('temperature', 0.8)
        )

        if not result.get('success'):
            raise Exception(f"OpenAI generation failed: {result.get('error')}")

        # Parse and structure the generated story
        story_text = result['story']
        structured_story = self._parse_openai_response(story_text)

        # Add generation metadata
        structured_story.update({
            "raw_response": story_text,
            "model_used": result.get('model'),
            "tokens_used": result.get('tokens_used')
        })

        return structured_story

    def _create_openai_prompt(self, **kwargs) -> str:
        """Create a prompt for OpenAI story generation."""
        genre = kwargs.get('genre') or random.choice(self.template_data['genres'])
        setting = kwargs.get('setting') or random.choice(self.template_data['settings'])
        mood = kwargs.get('mood') or random.choice(self.template_data['moods'])

        prompt = f"""Create a detailed story prompt for a {genre} story set in {setting} with a {mood} mood.

Please provide the story in the following JSON format:
{{
    "title": "Story title",
    "genre": "{genre}",
    "setting": "Detailed setting description",
    "main_characters": ["character1", "character2"],
    "plot_summary": "Detailed plot summary (100-300 words)",
    "mood": "{mood}",
    "target_image_style": "Visual style description for image generation"
}}

Make the story vivid and suitable for visual representation. Include specific details about characters, locations, and key scenes that would translate well to images."""

        return prompt

    def _parse_openai_response(self, response_text: str) -> Dict[str, Any]:
        """Parse OpenAI response into structured story data."""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                story_data = json.loads(json_match.group())
                return story_data
        except:
            pass

        # Fallback: create structure from text
        return {
            "title": "Generated Story",
            "genre": "unknown",
            "setting": "Generated setting",
            "main_characters": ["Generated character"],
            "plot_summary": response_text,
            "mood": "unknown",
            "target_image_style": "cinematic"
        }

    async def generate_with_template(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story using template-based approach.

        Args:
            prompt: Custom prompt (ignored for template method)
            **kwargs: Template parameters

        Returns:
            Generated story data
        """
        # Select random elements or use provided ones
        genre = kwargs.get('genre') or random.choice(self.template_data['genres'])
        setting = kwargs.get('setting') or random.choice(self.template_data['settings'])
        characters = kwargs.get('characters') or random.sample(
            self.template_data['characters'],
            random.randint(1, 3)
        )
        conflict = kwargs.get('conflict') or random.choice(self.template_data['conflicts'])
        mood = kwargs.get('mood') or random.choice(self.template_data['moods'])
        visual_style = kwargs.get('visual_style') or random.choice(self.template_data['visual_styles'])

        # Generate title
        title = f"The {random.choice(['Chronicles', 'Legend', 'Tale', 'Saga', 'Quest'])} of {characters[0].title()}"

        # Generate plot summary
        plot_templates = [
            f"In the {setting}, {characters[0]} embarks on a {conflict}. Joined by {', '.join(characters[1:]) if len(characters) > 1 else 'loyal companions'}, they must overcome great challenges in this {mood} adventure.",
            f"When darkness threatens the {setting}, {characters[0]} discovers they are the key to a {conflict}. With the help of {', '.join(characters[1:]) if len(characters) > 1 else 'unexpected allies'}, they face their destiny in this {mood} tale.",
            f"The {setting} holds ancient secrets that {characters[0]} must uncover in their {conflict}. Accompanied by {', '.join(characters[1:]) if len(characters) > 1 else 'faithful friends'}, they navigate a {mood} journey of discovery."
        ]

        plot_summary = random.choice(plot_templates)

        return {
            "title": title,
            "genre": genre,
            "setting": setting,
            "main_characters": characters,
            "plot_summary": plot_summary,
            "mood": mood,
            "target_image_style": visual_style
        }

    async def generate_interactive(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story through interactive CLI prompts.

        Args:
            prompt: Custom prompt (ignored for interactive method)
            **kwargs: Additional parameters

        Returns:
            Generated story data
        """
        print("\n🎭 Interactive Story Creation")
        print("=" * 40)

        # Get user inputs
        title = input("Story Title (or press Enter for auto-generation): ").strip()
        if not title:
            title = f"The {random.choice(['Chronicles', 'Legend', 'Tale', 'Saga', 'Quest'])} of {random.choice(['Mystery', 'Adventure', 'Discovery'])}"

        print(f"\nAvailable genres: {', '.join(self.template_data['genres'])}")
        genre = input("Choose genre (or press Enter for random): ").strip()
        if not genre or genre not in self.template_data['genres']:
            genre = random.choice(self.template_data['genres'])

        print(f"\nSample settings: {', '.join(self.template_data['settings'][:5])}...")
        setting = input("Describe the setting: ").strip()
        if not setting:
            setting = random.choice(self.template_data['settings'])

        characters_input = input("Main characters (comma-separated): ").strip()
        if characters_input:
            characters = [char.strip() for char in characters_input.split(',')]
        else:
            characters = random.sample(self.template_data['characters'], random.randint(1, 3))

        plot_summary = input("Plot summary (or press Enter for template): ").strip()
        if not plot_summary:
            conflict = random.choice(self.template_data['conflicts'])
            plot_summary = f"In the {setting}, {characters[0]} embarks on a {conflict}. This {genre} story follows their journey through challenges and discoveries."

        print(f"\nAvailable moods: {', '.join(self.template_data['moods'])}")
        mood = input("Choose mood (or press Enter for random): ").strip()
        if not mood or mood not in self.template_data['moods']:
            mood = random.choice(self.template_data['moods'])

        print(f"\nAvailable styles: {', '.join(self.template_data['visual_styles'])}")
        visual_style = input("Target image style (or press Enter for random): ").strip()
        if not visual_style or visual_style not in self.template_data['visual_styles']:
            visual_style = random.choice(self.template_data['visual_styles'])

        return {
            "title": title,
            "genre": genre,
            "setting": setting,
            "main_characters": characters,
            "plot_summary": plot_summary,
            "mood": mood,
            "target_image_style": visual_style
        }

    async def generate_random(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate completely random story using weighted selection.

        Args:
            prompt: Custom prompt (ignored for random method)
            **kwargs: Additional parameters

        Returns:
            Generated story data
        """
        # Weighted random selection for more interesting combinations
        genre_weights = {
            "fantasy": 0.25, "sci-fi": 0.20, "mystery": 0.15, "adventure": 0.15,
            "romance": 0.10, "horror": 0.10, "drama": 0.05
        }

        mood_weights = {
            "epic": 0.20, "mysterious": 0.15, "dark": 0.15, "hopeful": 0.15,
            "dramatic": 0.10, "romantic": 0.10, "eerie": 0.10, "peaceful": 0.05
        }

        # Weighted selection
        genre = self._weighted_choice(self.template_data['genres'], genre_weights)
        mood = self._weighted_choice(self.template_data['moods'], mood_weights)

        # Random selections
        setting = random.choice(self.template_data['settings'])
        num_characters = random.choices([1, 2, 3, 4], weights=[0.3, 0.4, 0.2, 0.1])[0]
        characters = random.sample(self.template_data['characters'], num_characters)
        conflict = random.choice(self.template_data['conflicts'])
        visual_style = random.choice(self.template_data['visual_styles'])

        # Generate creative title
        title_templates = [
            f"The {random.choice(['Lost', 'Hidden', 'Ancient', 'Forgotten', 'Sacred'])} {random.choice(['Realm', 'Kingdom', 'Secret', 'Power', 'Destiny'])}",
            f"{random.choice(['Chronicles', 'Legends', 'Tales', 'Saga'])} of the {random.choice(['Eternal', 'Mystic', 'Shadow', 'Crystal', 'Golden'])} {random.choice(['Warrior', 'Guardian', 'Oracle', 'Phoenix', 'Dragon'])}",
            f"The {random.choice(['Last', 'First', 'Chosen', 'Fallen', 'Rising'])} {characters[0].title()}"
        ]

        title = random.choice(title_templates)

        # Generate complex plot
        plot_elements = [
            f"In the mystical {setting}, an ancient prophecy speaks of {conflict}.",
            f"When {characters[0]} discovers their true heritage, they must unite with {', '.join(characters[1:]) if len(characters) > 1 else 'unlikely allies'}.",
            f"As darkness threatens to consume the realm, only the power of {random.choice(['friendship', 'sacrifice', 'wisdom', 'courage', 'love'])} can prevail.",
            f"Through trials of {random.choice(['fire', 'shadow', 'ice', 'light', 'time'])}, our heroes must {conflict} before it's too late."
        ]

        plot_summary = " ".join(random.sample(plot_elements, random.randint(2, 4)))

        return {
            "title": title,
            "genre": genre,
            "setting": setting,
            "main_characters": characters,
            "plot_summary": plot_summary,
            "mood": mood,
            "target_image_style": visual_style
        }

    def _weighted_choice(self, choices: List[str], weights: Dict[str, float]) -> str:
        """Make weighted random choice from list."""
        available_choices = [choice for choice in choices if choice in weights]
        if not available_choices:
            return random.choice(choices)

        choice_weights = [weights[choice] for choice in available_choices]
        return random.choices(available_choices, weights=choice_weights)[0]

    async def generate_buddha_scene(self, prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate story based on original Buddha scenes.

        Args:
            prompt: Custom prompt (ignored)
            **kwargs: Additional parameters

        Returns:
            Generated story data based on Buddha scenes
        """
        # Use parent class method to get Buddha scene
        scene_index = kwargs.get('scene_index')
        if scene_index is not None and 0 <= scene_index < len(self.buddha_scenes):
            scene = self.buddha_scenes[scene_index]
        else:
            scene = random.choice(self.buddha_scenes)

        # Convert to standard format
        return {
            "title": scene["title"],
            "genre": "spiritual",
            "setting": scene["location"],
            "main_characters": ["Buddha", "disciples", "devotees"],
            "plot_summary": scene["scene"],
            "mood": scene["mood"],
            "target_image_style": "spiritual art, golden tones, divine lighting"
        }

    def generate_image_prompt(self, story_data: Dict[str, Any]) -> str:
        """Generate optimized image prompt from story data.

        Args:
            story_data: Story data dictionary

        Returns:
            Optimized image generation prompt
        """
        # Extract key elements
        setting = story_data.get("setting", "")
        characters = story_data.get("main_characters", [])
        mood = story_data.get("mood", "")
        style = story_data.get("target_image_style", "cinematic")
        plot = story_data.get("plot_summary", "")

        # Build prompt components
        scene_description = f"A {style} scene depicting {plot[:100]}..."
        if characters:
            scene_description += f" featuring {', '.join(characters[:2])}"

        location_desc = f"set in {setting}"
        mood_desc = f"with a {mood} atmosphere"

        # Add visual enhancement keywords
        visual_enhancements = [
            "highly detailed", "8K resolution", "professional photography",
            "dramatic lighting", "cinematic composition", "masterpiece quality"
        ]

        # Combine all elements
        full_prompt = f"{scene_description}, {location_desc}, {mood_desc}. "
        full_prompt += f"Visual style: {style}. "
        full_prompt += f"Enhanced with: {', '.join(random.sample(visual_enhancements, 3))}."

        return full_prompt

    def export_story_collection(self, stories: List[Dict[str, Any]],
                              filename: str = "story_collection.json") -> str:
        """Export multiple stories to JSON file.

        Args:
            stories: List of story data dictionaries
            filename: Output filename

        Returns:
            Path to exported file
        """
        sanitized_filename = sanitize_filename(filename)

        export_data = {
            "collection_metadata": {
                "total_stories": len(stories),
                "generated_at": asyncio.get_event_loop().time(),
                "generator_version": "1.0.0"
            },
            "stories": stories
        }

        with open(sanitized_filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        logger.info(f"Exported {len(stories)} stories to {sanitized_filename}")
        return sanitized_filename
