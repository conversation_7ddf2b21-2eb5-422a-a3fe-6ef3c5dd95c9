# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_organization_id_here

# Stable Diffusion API (if using external service)
STABILITY_API_KEY=your_stability_ai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Pipeline Configuration
DEFAULT_OUTPUT_DIR=output
DEFAULT_IMAGE_SIZE=1024x1024
DEFAULT_VIDEO_DURATION=8
DEFAULT_EFFECTS_INTENSITY=medium

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=storytelling_pipeline.log

# Rate Limiting
API_RATE_LIMIT_REQUESTS_PER_MINUTE=60
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=2

# Quality Settings
IMAGE_QUALITY=high
VIDEO_FPS=24
AUDIO_BITRATE=128k

# Cache Settings
ENABLE_CACHE=true
CACHE_DURATION_HOURS=24
CACHE_DIR=.cache
