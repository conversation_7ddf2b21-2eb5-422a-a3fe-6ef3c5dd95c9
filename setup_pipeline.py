#!/usr/bin/env python3
"""
Setup Script for Storytelling Pipeline

This script helps users set up the storytelling pipeline with proper configuration,
dependency checking, and initial validation.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
import pkg_resources

console = Console()


def check_python_version():
    """Check if Python version is compatible."""
    console.print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        console.print("❌ [red]Python 3.8+ is required[/red]")
        console.print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    console.print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    console.print("📦 Checking dependencies...")
    
    required_packages = []
    with open('requirements.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                package = line.split('==')[0].split('>=')[0].split('<=')[0]
                required_packages.append(package)
    
    missing_packages = []
    installed_packages = []
    
    for package in required_packages:
        try:
            pkg_resources.get_distribution(package)
            installed_packages.append(package)
        except pkg_resources.DistributionNotFound:
            missing_packages.append(package)
    
    # Create status table
    table = Table(title="Dependency Status")
    table.add_column("Package", style="cyan")
    table.add_column("Status", style="green")
    
    for package in installed_packages:
        table.add_row(package, "✅ Installed")
    
    for package in missing_packages:
        table.add_row(package, "❌ Missing")
    
    console.print(table)
    
    if missing_packages:
        console.print(f"\n⚠️  [yellow]{len(missing_packages)} packages need to be installed[/yellow]")
        if Confirm.ask("Install missing packages now?"):
            install_dependencies()
        else:
            console.print("❌ Setup cannot continue without required dependencies")
            return False
    else:
        console.print("✅ All dependencies are installed")
    
    return True


def install_dependencies():
    """Install required dependencies."""
    console.print("📥 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        console.print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        console.print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_configuration():
    """Set up configuration files."""
    console.print("⚙️  Setting up configuration...")
    
    # Copy example config if it doesn't exist
    if not Path("config.yaml").exists():
        if Path("config.yaml.example").exists():
            shutil.copy("config.yaml.example", "config.yaml")
            console.print("✅ Created config.yaml from example")
        else:
            console.print("⚠️  config.yaml.example not found")
    else:
        console.print("✅ config.yaml already exists")
    
    # Copy example .env if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            console.print("✅ Created .env from example")
        else:
            console.print("⚠️  .env.example not found")
    else:
        console.print("✅ .env already exists")
    
    return True


def configure_api_keys():
    """Help user configure API keys."""
    console.print("🔑 Configuring API keys...")
    
    env_file = Path(".env")
    if not env_file.exists():
        console.print("❌ .env file not found")
        return False
    
    # Read current .env content
    env_content = env_file.read_text()
    
    # Check for API keys
    has_openai = "OPENAI_API_KEY=" in env_content and not env_content.split("OPENAI_API_KEY=")[1].split("\n")[0].strip().endswith("_here")
    has_stability = "STABILITY_API_KEY=" in env_content and not env_content.split("STABILITY_API_KEY=")[1].split("\n")[0].strip().endswith("_here")
    
    console.print(f"OpenAI API Key: {'✅ Configured' if has_openai else '❌ Not configured'}")
    console.print(f"Stability AI API Key: {'✅ Configured' if has_stability else '❌ Not configured'}")
    
    if not has_openai and not has_stability:
        console.print("\n⚠️  [yellow]No API keys configured[/yellow]")
        console.print("The pipeline can work with template-based generation without API keys,")
        console.print("but AI-powered features will be unavailable.")
        
        if Confirm.ask("Would you like to configure API keys now?"):
            configure_keys_interactive()
    
    return True


def configure_keys_interactive():
    """Interactive API key configuration."""
    console.print("\n🔧 Interactive API Key Configuration")
    console.print("You can skip any key by pressing Enter")
    
    env_file = Path(".env")
    env_content = env_file.read_text()
    
    # OpenAI API Key
    console.print("\n📝 OpenAI API Key (for GPT story generation and DALL-E images)")
    console.print("Get your key from: https://platform.openai.com/api-keys")
    openai_key = Prompt.ask("Enter OpenAI API key (or press Enter to skip)", default="")
    
    if openai_key:
        env_content = env_content.replace("OPENAI_API_KEY=your_openai_api_key_here", f"OPENAI_API_KEY={openai_key}")
        console.print("✅ OpenAI API key configured")
    
    # Stability AI API Key
    console.print("\n🎨 Stability AI API Key (for Stable Diffusion images)")
    console.print("Get your key from: https://platform.stability.ai/")
    stability_key = Prompt.ask("Enter Stability AI API key (or press Enter to skip)", default="")
    
    if stability_key:
        env_content = env_content.replace("STABILITY_API_KEY=your_stability_ai_api_key_here", f"STABILITY_API_KEY={stability_key}")
        console.print("✅ Stability AI API key configured")
    
    # Save updated .env file
    env_file.write_text(env_content)
    console.print("💾 Configuration saved to .env file")


def create_output_directory():
    """Create output directory structure."""
    console.print("📁 Creating output directory structure...")
    
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    subdirs = ["examples", "templates", "cache"]
    for subdir in subdirs:
        (output_dir / subdir).mkdir(exist_ok=True)
    
    console.print("✅ Output directory structure created")
    return True


def test_pipeline():
    """Test basic pipeline functionality."""
    console.print("🧪 Testing pipeline functionality...")
    
    try:
        # Import and test basic functionality
        from core.pipeline import StorytellingPipeline
        
        pipeline = StorytellingPipeline()
        status = pipeline.get_pipeline_status()
        validation = pipeline.validate_pipeline_setup()
        
        console.print("✅ Pipeline imports and initializes correctly")
        
        # Show validation results
        if validation['valid']:
            console.print("✅ Pipeline validation passed")
        else:
            console.print("⚠️  Pipeline has validation warnings (this is normal without API keys)")
        
        return True
        
    except Exception as e:
        console.print(f"❌ Pipeline test failed: {e}")
        return False


def show_next_steps():
    """Show next steps to the user."""
    console.print(Panel("🚀 Setup Complete! Next Steps", style="green"))
    
    console.print("1. 📖 [bold]Read the documentation[/bold]:")
    console.print("   • Check README.md for detailed usage instructions")
    console.print("   • Review examples/sample_configs/ for configuration options")
    
    console.print("\n2. 🎭 [bold]Try the demo[/bold]:")
    console.print("   python examples/demo_pipeline.py")
    
    console.print("\n3. 🎯 [bold]Generate your first story[/bold]:")
    console.print("   python main_pipeline.py generate --method template")
    
    console.print("\n4. 📊 [bold]Check pipeline status[/bold]:")
    console.print("   python main_pipeline.py status")
    
    console.print("\n5. 🔧 [bold]Customize configuration[/bold]:")
    console.print("   • Edit config.yaml for pipeline settings")
    console.print("   • Edit .env for API keys and environment variables")
    
    console.print("\n6. 🧪 [bold]Run tests[/bold]:")
    console.print("   pytest tests/ -v")
    
    console.print("\n💡 [bold]Tips[/bold]:")
    console.print("   • The pipeline works without API keys using template generation")
    console.print("   • Add OpenAI API key for AI-powered story generation")
    console.print("   • Add Stability AI API key for image generation fallback")
    console.print("   • Check the output/ directory for generated content")


def main():
    """Main setup function."""
    console.print(Panel("🎭 Storytelling Pipeline Setup", style="bold blue"))
    console.print("This script will help you set up the automated storytelling pipeline.\n")
    
    setup_steps = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Configuration Files", setup_configuration),
        ("API Keys", configure_api_keys),
        ("Output Directory", create_output_directory),
        ("Pipeline Test", test_pipeline)
    ]
    
    failed_steps = []
    
    for step_name, step_function in setup_steps:
        console.print(f"\n📋 Step: {step_name}")
        try:
            if not step_function():
                failed_steps.append(step_name)
        except Exception as e:
            console.print(f"❌ {step_name} failed: {e}")
            failed_steps.append(step_name)
    
    console.print("\n" + "="*50)
    
    if failed_steps:
        console.print(f"⚠️  [yellow]Setup completed with {len(failed_steps)} issues[/yellow]")
        console.print("Failed steps:", ", ".join(failed_steps))
        console.print("\nYou may need to resolve these issues manually.")
    else:
        console.print("✅ [green]Setup completed successfully![/green]")
    
    show_next_steps()


if __name__ == "__main__":
    main()
