2025-07-23 12:16:48,693 - config.settings - INFO - Logging configured successfully
2025-07-23 12:16:49,077 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:16:49,375 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:16:49,375 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:16:49,376 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:19:10,635 - config.settings - INFO - Logging configured successfully
2025-07-23 12:19:10,976 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:19:11,292 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:19:11,292 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:19:11,292 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:19:11,296 - utils.file_manager - INFO - Created session directory: output\20250723_121911
2025-07-23 12:19:11,296 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_121911
2025-07-23 12:19:11,296 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:19:11,296 - core.story_generator - INFO - Generating story using method: template
2025-07-23 12:19:11,301 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:19:11,302 - core.story_generator - INFO - Story generated successfully using template
2025-07-23 12:19:11,303 - utils.file_manager - INFO - Saved story data: output\20250723_121911\stories\story_121911.json
2025-07-23 12:19:11,304 - core.pipeline - INFO - Story saved: output\20250723_121911\stories\story_121911.json
2025-07-23 12:19:11,304 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:19:11,309 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:19:11,310 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:19:12,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:19:12,519 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:12,520 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:12,521 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:19:13,651 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:16,193 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:20,600 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:28,974 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:28,975 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:28,976 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:28,977 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:28,992 - config.settings - INFO - Logging configured successfully
2025-07-23 12:19:29,326 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:19:29,618 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:19:29,619 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:19:29,619 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:19:29,632 - utils.file_manager - INFO - Created session directory: output\20250723_121929
2025-07-23 12:19:29,632 - core.pipeline - INFO - Starting batch generation of 3 stories in session: output\20250723_121929
2025-07-23 12:19:29,633 - core.pipeline - INFO - Generating story 1/3
2025-07-23 12:19:29,635 - utils.file_manager - INFO - Created session directory: output\20250723_121929
2025-07-23 12:19:29,635 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_121929
2025-07-23 12:19:29,636 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:19:29,636 - core.story_generator - INFO - Generating story using method: random
2025-07-23 12:19:29,645 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:19:29,645 - core.story_generator - INFO - Story generated successfully using random
2025-07-23 12:19:29,647 - utils.file_manager - INFO - Saved story data: output\20250723_121929\stories\story_121929.json
2025-07-23 12:19:29,647 - core.pipeline - INFO - Story saved: output\20250723_121929\stories\story_121929.json
2025-07-23 12:19:29,647 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:19:29,651 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:19:29,652 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:19:30,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:19:30,628 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:30,629 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:30,630 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:19:31,547 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:33,900 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:38,216 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:46,528 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:46,528 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:46,529 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:46,529 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:46,530 - core.pipeline - INFO - Generating story 2/3
2025-07-23 12:19:46,533 - utils.file_manager - INFO - Created session directory: output\20250723_121946
2025-07-23 12:19:46,533 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_121946
2025-07-23 12:19:46,534 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:19:46,534 - core.story_generator - INFO - Generating story using method: random
2025-07-23 12:19:46,541 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:19:46,541 - core.story_generator - INFO - Story generated successfully using random
2025-07-23 12:19:46,543 - utils.file_manager - INFO - Saved story data: output\20250723_121946\stories\story_121946.json
2025-07-23 12:19:46,543 - core.pipeline - INFO - Story saved: output\20250723_121946\stories\story_121946.json
2025-07-23 12:19:46,544 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:19:46,550 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:19:46,550 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:19:47,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:19:47,114 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:47,114 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:19:47,115 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:19:47,414 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:49,774 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:19:54,077 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:02,413 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:02,414 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:02,414 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:02,415 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:02,416 - core.pipeline - INFO - Generating story 3/3
2025-07-23 12:20:02,419 - utils.file_manager - INFO - Created session directory: output\20250723_122002
2025-07-23 12:20:02,419 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_122002
2025-07-23 12:20:02,420 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:20:02,421 - core.story_generator - INFO - Generating story using method: random
2025-07-23 12:20:02,432 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:20:02,432 - core.story_generator - INFO - Story generated successfully using random
2025-07-23 12:20:02,434 - utils.file_manager - INFO - Saved story data: output\20250723_122002\stories\story_122002.json
2025-07-23 12:20:02,435 - core.pipeline - INFO - Story saved: output\20250723_122002\stories\story_122002.json
2025-07-23 12:20:02,435 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:20:02,444 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:20:02,444 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:20:02,924 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:20:02,926 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:02,927 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:02,928 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:20:03,214 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:05,541 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:09,944 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:18,281 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:18,282 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:18,283 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:18,284 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:18,299 - utils.file_manager - INFO - Created session summary: output\20250723_122002\session_summary.json
2025-07-23 12:20:18,300 - core.pipeline - INFO - Batch generation completed: 0/3 successful
2025-07-23 12:20:18,312 - config.settings - INFO - Logging configured successfully
2025-07-23 12:20:18,629 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:20:18,913 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:20:18,913 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:20:18,913 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:20:18,916 - utils.file_manager - INFO - Created session directory: output\20250723_122018
2025-07-23 12:20:18,917 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_122018
2025-07-23 12:20:18,917 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:20:18,917 - core.story_generator - INFO - Generating story using method: buddha
2025-07-23 12:20:18,918 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F842A95F90>
2025-07-23 12:20:18,919 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001F842A71730>, 215456.0827634)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001F842A96AD0>
2025-07-23 12:20:18,920 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F842A881A0>
2025-07-23 12:20:18,926 - utils.validators - ERROR - Story prompt validation failed: 'spiritual' is not one of ['fantasy', 'sci-fi', 'mystery', 'romance', 'adventure', 'horror', 'drama']
2025-07-23 12:20:18,927 - core.story_generator - INFO - Story generated successfully using buddha
2025-07-23 12:20:18,928 - utils.file_manager - INFO - Saved story data: output\20250723_122018\stories\story_122018.json
2025-07-23 12:20:18,928 - core.pipeline - INFO - Story saved: output\20250723_122018\stories\story_122018.json
2025-07-23 12:20:18,928 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:20:18,932 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:20:18,933 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:20:19,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:20:19,535 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:19,536 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:19,537 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:20:20,748 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:23,122 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:27,487 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:35,876 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:35,877 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:35,878 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:35,879 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:35,890 - config.settings - INFO - Logging configured successfully
2025-07-23 12:20:36,230 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:20:36,527 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:20:36,528 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:20:36,528 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:20:36,531 - utils.file_manager - INFO - Created session directory: output\20250723_122036
2025-07-23 12:20:36,531 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_122036
2025-07-23 12:20:36,532 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:20:36,532 - core.story_generator - INFO - Generating story using method: template
2025-07-23 12:20:36,536 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:20:36,537 - core.story_generator - INFO - Story generated successfully using template
2025-07-23 12:20:36,538 - utils.file_manager - INFO - Saved story data: output\20250723_122036\stories\story_122036.json
2025-07-23 12:20:36,539 - core.pipeline - INFO - Story saved: output\20250723_122036\stories\story_122036.json
2025-07-23 12:20:36,540 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:20:36,546 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:20:36,546 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:20:37,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:20:37,075 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:37,076 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:37,076 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:20:37,953 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:40,317 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:44,660 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:52,942 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:52,942 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:52,943 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:52,944 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:52,949 - config.settings - INFO - Logging configured successfully
2025-07-23 12:20:53,292 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:20:53,670 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:20:53,671 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:20:53,671 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:20:53,684 - config.settings - INFO - Logging configured successfully
2025-07-23 12:20:54,004 - core.story_generator - INFO - OpenAI client initialized successfully
2025-07-23 12:20:54,308 - core.image_generator - INFO - OpenAI DALL-E client initialized
2025-07-23 12:20:54,309 - core.image_generator - INFO - Stability AI client initialized
2025-07-23 12:20:54,309 - core.pipeline - INFO - Storytelling pipeline initialized successfully
2025-07-23 12:20:54,315 - utils.file_manager - INFO - Created session directory: output\20250723_122054
2025-07-23 12:20:54,315 - core.pipeline - INFO - Starting complete story generation in session: output\20250723_122054
2025-07-23 12:20:54,315 - core.pipeline - INFO - Progress: 1/4 - Generating story prompt
2025-07-23 12:20:54,315 - core.story_generator - INFO - Generating story using method: template
2025-07-23 12:20:54,320 - utils.validators - INFO - Story prompt validation completed successfully
2025-07-23 12:20:54,320 - core.story_generator - INFO - Story generated successfully using template
2025-07-23 12:20:54,321 - utils.file_manager - INFO - Saved story data: output\20250723_122054\stories\story_122054.json
2025-07-23 12:20:54,322 - core.pipeline - INFO - Story saved: output\20250723_122054\stories\story_122054.json
2025-07-23 12:20:54,322 - core.pipeline - INFO - Progress: 2/4 - Generating image from story
2025-07-23 12:20:54,327 - utils.validators - INFO - Image parameters validation completed successfully
2025-07-23 12:20:54,328 - core.image_generator - INFO - Attempting image generation with openai
2025-07-23 12:20:54,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/images/generations "HTTP/1.1 401 Unauthorized"
2025-07-23 12:20:54,695 - utils.api_client - ERROR - OpenAI image generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:54,696 - core.image_generator - WARNING - openai generation failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-23 12:20:54,697 - core.image_generator - INFO - Attempting image generation with stability
2025-07-23 12:20:55,112 - utils.api_client - WARNING - Request failed (attempt 1), retrying in 2.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:20:57,484 - utils.api_client - WARNING - Request failed (attempt 2), retrying in 4.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:01,859 - utils.api_client - WARNING - Request failed (attempt 3), retrying in 8.0s: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:10,227 - utils.api_client - ERROR - Request failed after 4 attempts: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:10,228 - utils.api_client - ERROR - Stability AI image generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:10,229 - core.image_generator - WARNING - stability generation failed: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:10,230 - core.pipeline - ERROR - Pipeline execution failed: All image generation services failed. Last error: 401, message='Unauthorized', url='https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image'
2025-07-23 12:21:10,349 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F842A974D0>
2025-07-23 12:21:10,350 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F842A97390>
2025-07-23 12:21:10,351 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F842A97250>
2025-07-23 12:21:10,351 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001F842A72510>, 215508.0284252)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001F842A97110>
