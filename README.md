# Automated Storytelling and Visual Content Generation Pipeline

A comprehensive Python-based system for automated storytelling and visual content generation, featuring AI-powered story creation, image generation, and visual effects processing.

## 🌟 Features

### 📝 Story Generation
- **Multiple Generation Methods**: OpenAI GPT, template-based, interactive CLI, random generation, and Buddha-themed stories
- **Intelligent Prompt Engineering**: Converts story elements to optimized image prompts
- **Content Validation**: Built-in validation and content filtering
- **Export Capabilities**: JSON and CSV export formats

### 🎨 Image Generation
- **Multi-Service Support**: OpenAI DALL-E 3 and Stable Diffusion API integration
- **Intelligent Fallback**: Automatic service switching for reliability
- **Quality Validation**: Automatic image quality assessment and retry logic
- **Metadata Embedding**: Comprehensive metadata tracking

### ✨ Visual Effects Processing
- **Color Effects**: Vintage, vibrant, dramatic, soft filters
- **Artistic Effects**: Oil painting, watercolor, sketch, HDR styles
- **Motion Effects**: Ken Burns, zoom, fade transitions
- **Multiple Formats**: Static images (PNG, JPG) and animated content (GIF, MP4)

### 🔄 Pipeline Orchestration
- **Complete Workflow**: Story → Image → Effects → Final Output
- **Batch Processing**: Generate multiple stories simultaneously
- **Video Sequences**: Create cinematic video sequences from story collections
- **Progress Tracking**: Real-time progress updates and ETA estimation
- **Error Handling**: Graceful degradation and comprehensive error reporting

## 🚀 Quick Start

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd project0
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Configure API keys**:
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Copy configuration**:
```bash
cp config.yaml.example config.yaml
# Customize settings as needed
```

### Basic Usage

#### Generate a Single Story
```bash
python main_pipeline.py generate --method random --format png
```

#### Generate Multiple Stories
```bash
python main_pipeline.py batch --count 5 --method template
```

#### Create Video Sequence
```bash
python main_pipeline.py video --count 3 --duration 10 --audio background.mp3
```

#### Check Pipeline Status
```bash
python main_pipeline.py status
```

## 📋 Requirements

### System Requirements
- Python 3.8+
- 4GB+ RAM recommended
- 2GB+ free disk space for outputs
- Internet connection for API services

### API Keys (Optional but Recommended)
- **OpenAI API Key**: For GPT story generation and DALL-E image generation
- **Stability AI API Key**: For Stable Diffusion image generation (fallback)
- **Hugging Face API Key**: For additional model access (optional)

## ⚙️ Configuration

### Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_organization_id_here

# Stability AI Configuration  
STABILITY_API_KEY=your_stability_ai_api_key_here

# Pipeline Settings
DEFAULT_OUTPUT_DIR=output
LOG_LEVEL=INFO
```

### Configuration File (config.yaml)
```yaml
story_generation:
  default_method: "openai"
  openai:
    model: "gpt-4"
    temperature: 0.8

image_generation:
  primary_service: "openai"
  fallback_service: "stability"
  
effects_processing:
  default_intensity: "medium"
  output_formats:
    static: ["png", "jpg"]
    animated: ["gif", "mp4"]
```

## 🎯 Usage Examples

### Python API Usage

```python
from core.pipeline import StorytellingPipeline

# Initialize pipeline
pipeline = StorytellingPipeline()

# Generate complete story with image and effects
result = await pipeline.generate_complete_story(
    story_method="random",
    image_service="openai", 
    effects=["dramatic", "hdr"],
    output_format="png"
)

if result["success"]:
    print(f"Story created: {result['story_data']['title']}")
    print(f"Final output: {result['final_output']}")
```

### Advanced Batch Processing

```python
# Generate batch of stories
batch_result = await pipeline.generate_batch_stories(
    count=10,
    story_method="template",
    output_format="gif"
)

print(f"Success rate: {batch_result['successful_generations']}/{batch_result['total_requested']}")
```

### Video Sequence Creation

```python
# Create cinematic video sequence
video_result = await pipeline.create_story_video_sequence(
    story_count=5,
    duration_per_scene=8.0,
    audio_path="background_music.mp3"
)

if video_result["success"]:
    print(f"Video created: {video_result['video_path']}")
```

## 📁 Project Structure

```
project0/
├── core/                   # Core pipeline components
│   ├── story_generator.py  # Enhanced story generation
│   ├── image_generator.py  # AI image generation
│   ├── effects_processor.py # Visual effects processing
│   └── pipeline.py         # Main orchestration
├── config/                 # Configuration management
│   └── settings.py         # Config loading and validation
├── utils/                  # Utility functions
│   ├── api_client.py       # API client wrappers
│   ├── file_manager.py     # File operations
│   └── validators.py       # Input validation
├── tests/                  # Test suite
├── examples/               # Example configurations and outputs
├── main_pipeline.py        # CLI entry point
├── requirements.txt        # Python dependencies
├── config.yaml.example     # Example configuration
└── .env.example           # Environment variables template
```

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_story_generator.py -v

# Run with coverage
pytest tests/ --cov=core --cov-report=html
```

## 🔧 Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure API keys are correctly set in `.env` file
   - Check API key permissions and quotas
   - Verify internet connection

2. **Memory Issues**
   - Reduce batch size for large operations
   - Use lower resolution images
   - Clear output directory periodically

3. **Video Generation Fails**
   - Install FFmpeg for video processing
   - Check available disk space
   - Verify image files exist and are valid

### Performance Optimization

- **Use SSD storage** for better I/O performance
- **Configure batch sizes** based on available memory
- **Enable caching** for repeated operations
- **Use multiple API keys** for higher rate limits

## 📊 Performance Benchmarks

| Operation | Time (avg) | Memory Usage | Output Size |
|-----------|------------|--------------|-------------|
| Story Generation (Template) | 0.1s | 50MB | 2KB |
| Story Generation (OpenAI) | 3-5s | 100MB | 2KB |
| Image Generation (DALL-E) | 10-15s | 200MB | 2-5MB |
| Effects Processing | 2-5s | 300MB | 2-10MB |
| Video Creation (5 scenes) | 30-60s | 500MB | 50-200MB |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT and DALL-E APIs
- Stability AI for Stable Diffusion
- The open-source community for the excellent libraries used in this project

## 📞 Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the troubleshooting guide
- Review the test suite for usage examples

---

**Happy Storytelling! 🎭✨**
