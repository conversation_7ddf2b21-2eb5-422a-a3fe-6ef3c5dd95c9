"""
Image Generation Module

Creates ImageGenerator class with OpenAI DALL-E 3 and Stable Diffusion API integration,
intelligent prompt engineering, retry logic, and metadata embedding.
"""

import asyncio
import base64
import logging
import time
import requests
from io import BytesIO
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from PIL import Image, ExifTags
import json

from config.settings import load_config
from utils.api_client import OpenAIClient, StabilityClient
from utils.validators import validate_image_params, validate_content_safety
from utils.file_manager import FileManager

logger = logging.getLogger(__name__)


class ImageGenerator:
    """Handles AI image generation with multiple service support and intelligent prompt engineering."""

    def __init__(self, config_manager=None, file_manager=None):
        """Initialize the image generator.

        Args:
            config_manager: Configuration manager instance
            file_manager: File manager instance
        """
        self.config = config_manager or load_config()
        self.file_manager = file_manager or FileManager()
        self.image_config = self.config.get_image_config()

        # Initialize API clients
        self.openai_client = None
        self.stability_client = None
        self._init_api_clients()

        # Load prompt enhancement templates
        self.prompt_enhancements = self._load_prompt_enhancements()

        # Service priority
        self.service_priority = self._get_service_priority()

    def _init_api_clients(self):
        """Initialize available API clients."""
        api_keys = self.config.get_api_keys()

        # Initialize OpenAI client
        if api_keys.get('openai'):
            try:
                self.openai_client = OpenAIClient(
                    api_key=api_keys['openai'],
                    organization_id=api_keys.get('openai_org_id')
                )
                logger.info("OpenAI DALL-E client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")

        # Initialize Stability AI client
        if api_keys.get('stability'):
            try:
                self.stability_client = StabilityClient(
                    api_key=api_keys['stability']
                )
                logger.info("Stability AI client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Stability AI client: {e}")

    def _load_prompt_enhancements(self) -> Dict[str, List[str]]:
        """Load prompt enhancement templates."""
        enhancement_config = self.image_config.get('prompt_enhancement', {})

        return {
            "style_keywords": enhancement_config.get('style_keywords', [
                "cinematic", "highly detailed", "8K resolution", "professional photography",
                "masterpiece", "best quality", "ultra-detailed", "photorealistic"
            ]),
            "lighting_keywords": enhancement_config.get('lighting_keywords', [
                "dramatic lighting", "golden hour", "soft shadows", "volumetric lighting",
                "rim lighting", "ambient lighting", "natural lighting"
            ]),
            "composition_keywords": enhancement_config.get('composition_keywords', [
                "rule of thirds", "dynamic composition", "perfect framing",
                "depth of field", "bokeh background", "sharp focus"
            ]),
            "quality_keywords": enhancement_config.get('quality_keywords', [
                "trending on artstation", "award winning", "professional grade",
                "studio quality", "high resolution", "crisp details"
            ])
        }

    def _get_service_priority(self) -> List[str]:
        """Get service priority order from configuration."""
        primary = self.image_config.get('primary_service', 'openai')
        fallback = self.image_config.get('fallback_service', 'stability')

        priority = []
        if primary == 'openai' and self.openai_client:
            priority.append('openai')
        elif primary == 'stability' and self.stability_client:
            priority.append('stability')

        if fallback == 'openai' and self.openai_client and 'openai' not in priority:
            priority.append('openai')
        elif fallback == 'stability' and self.stability_client and 'stability' not in priority:
            priority.append('stability')

        return priority or ['openai', 'stability']  # Default fallback

    async def generate_image(self, prompt: str, story_data: Optional[Dict[str, Any]] = None,
                           service: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate image from prompt using available services.

        Args:
            prompt: Image generation prompt
            story_data: Optional story data for context
            service: Specific service to use (openai, stability, auto)
            **kwargs: Additional generation parameters

        Returns:
            Generated image data with metadata
        """
        # Enhance prompt
        enhanced_prompt = self.enhance_prompt(prompt, story_data)

        # Validate parameters
        image_params = {
            "prompt": enhanced_prompt,
            **kwargs
        }

        validation_result = validate_image_params(image_params)
        if not validation_result["valid"]:
            raise ValueError(f"Invalid image parameters: {validation_result['errors']}")

        # Content safety check
        safety_result = validate_content_safety(enhanced_prompt)
        if not safety_result["safe"]:
            logger.warning(f"Content safety concerns: {safety_result['issues']}")

        # Select service
        if service == "auto" or service is None:
            services_to_try = self.service_priority
        else:
            services_to_try = [service]

        # Try services in order
        last_error = None
        for service_name in services_to_try:
            try:
                logger.info(f"Attempting image generation with {service_name}")

                if service_name == "openai" and self.openai_client:
                    result = await self._generate_with_openai(enhanced_prompt, **kwargs)
                elif service_name == "stability" and self.stability_client:
                    result = await self._generate_with_stability(enhanced_prompt, **kwargs)
                else:
                    continue

                if result.get("success"):
                    # Add metadata
                    result.update({
                        "original_prompt": prompt,
                        "enhanced_prompt": enhanced_prompt,
                        "story_context": story_data,
                        "validation": validation_result,
                        "content_safety": safety_result,
                        "service_used": service_name
                    })

                    # Save image and metadata
                    saved_path = await self._save_generated_image(result)
                    result["saved_path"] = saved_path

                    logger.info(f"Image generated successfully with {service_name}")
                    return result
                else:
                    last_error = result.get("error", "Unknown error")
                    logger.warning(f"{service_name} generation failed: {last_error}")

            except Exception as e:
                last_error = str(e)
                logger.error(f"Error with {service_name}: {e}")
                continue

        # All services failed
        raise Exception(f"All image generation services failed. Last error: {last_error}")

    async def _generate_with_openai(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate image using OpenAI DALL-E."""
        openai_config = self.image_config.get('openai', {})

        # Prepare parameters
        params = {
            "size": kwargs.get("size", openai_config.get("size", "1024x1024")),
            "quality": kwargs.get("quality", openai_config.get("quality", "hd")),
            "style": kwargs.get("style", openai_config.get("style", "vivid"))
        }

        return await self.openai_client.generate_image(prompt, **params)

    async def _generate_with_stability(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate image using Stability AI."""
        stability_config = self.image_config.get('stability', {})

        # Parse size parameter
        size = kwargs.get("size", "1024x1024")
        if "x" in size:
            width, height = map(int, size.split("x"))
        else:
            width = height = 1024

        # Prepare parameters
        params = {
            "model": kwargs.get("model", stability_config.get("model", "stable-diffusion-xl-1024-v1-0")),
            "width": width,
            "height": height,
            "steps": kwargs.get("steps", stability_config.get("steps", 30)),
            "cfg_scale": kwargs.get("cfg_scale", stability_config.get("cfg_scale", 7.0))
        }

        return await self.stability_client.generate_image(prompt, **params)

    def enhance_prompt(self, prompt: str, story_data: Optional[Dict[str, Any]] = None) -> str:
        """Enhance prompt with style keywords and story context.

        Args:
            prompt: Original prompt
            story_data: Optional story data for context enhancement

        Returns:
            Enhanced prompt
        """
        enhanced_parts = [prompt]

        # Add story context if available
        if story_data:
            mood = story_data.get("mood")
            genre = story_data.get("genre")
            style = story_data.get("target_image_style")

            if mood:
                enhanced_parts.append(f"mood: {mood}")
            if genre:
                enhanced_parts.append(f"genre: {genre}")
            if style:
                enhanced_parts.append(f"style: {style}")

        # Add enhancement keywords
        import random

        style_keywords = random.sample(
            self.prompt_enhancements["style_keywords"],
            min(2, len(self.prompt_enhancements["style_keywords"]))
        )
        lighting_keywords = random.sample(
            self.prompt_enhancements["lighting_keywords"],
            min(1, len(self.prompt_enhancements["lighting_keywords"]))
        )
        quality_keywords = random.sample(
            self.prompt_enhancements["quality_keywords"],
            min(1, len(self.prompt_enhancements["quality_keywords"]))
        )

        enhanced_parts.extend(style_keywords)
        enhanced_parts.extend(lighting_keywords)
        enhanced_parts.extend(quality_keywords)

        return ", ".join(enhanced_parts)

    async def _save_generated_image(self, image_data: Dict[str, Any]) -> str:
        """Save generated image and metadata to files.

        Args:
            image_data: Image generation result data

        Returns:
            Path to saved image file
        """
        if not self.file_manager.get_session_dir():
            self.file_manager.create_session_directory()

        # Generate filename
        timestamp = int(time.time())
        service = image_data.get("service_used", "unknown")
        filename = f"generated_image_{service}_{timestamp}"

        # Save image
        image_path = None
        if "image_url" in image_data:
            # Download from URL (OpenAI)
            image_path = await self._download_image(image_data["image_url"], filename)
        elif "image_data" in image_data:
            # Save from base64 data (Stability AI)
            image_path = self._save_base64_image(image_data["image_data"], filename)

        if image_path:
            # Save metadata
            metadata_path = self.file_manager.save_image_metadata(image_data, image_path.name)
            logger.info(f"Saved image: {image_path}, metadata: {metadata_path}")

            return str(image_path)
        else:
            raise Exception("Failed to save generated image")

    async def _download_image(self, url: str, filename: str) -> Path:
        """Download image from URL."""
        import aiohttp

        images_dir = self.file_manager.get_session_dir() / "images"
        file_path = images_dir / f"{filename}.png"

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    with open(file_path, 'wb') as f:
                        f.write(await response.read())
                    return file_path
                else:
                    raise Exception(f"Failed to download image: HTTP {response.status}")

    def _save_base64_image(self, base64_data: str, filename: str) -> Path:
        """Save image from base64 data."""
        images_dir = self.file_manager.get_session_dir() / "images"
        file_path = images_dir / f"{filename}.png"

        # Decode and save
        image_bytes = base64.b64decode(base64_data)
        image = Image.open(BytesIO(image_bytes))
        image.save(file_path, "PNG")

        return file_path

    async def generate_batch(self, prompts: List[str], story_data_list: Optional[List[Dict[str, Any]]] = None,
                           **kwargs) -> List[Dict[str, Any]]:
        """Generate multiple images in batch.

        Args:
            prompts: List of image prompts
            story_data_list: Optional list of story data for each prompt
            **kwargs: Additional generation parameters

        Returns:
            List of generation results
        """
        if story_data_list and len(story_data_list) != len(prompts):
            raise ValueError("story_data_list length must match prompts length")

        results = []
        batch_size = self.config.get('pipeline.performance.batch_size', 5)

        # Process in batches to avoid overwhelming APIs
        for i in range(0, len(prompts), batch_size):
            batch_prompts = prompts[i:i + batch_size]
            batch_story_data = story_data_list[i:i + batch_size] if story_data_list else [None] * len(batch_prompts)

            # Create tasks for concurrent processing
            tasks = []
            for prompt, story_data in zip(batch_prompts, batch_story_data):
                task = self.generate_image(prompt, story_data, **kwargs)
                tasks.append(task)

            # Execute batch
            try:
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"Batch item {i + j} failed: {result}")
                        results.append({
                            "success": False,
                            "error": str(result),
                            "prompt_index": i + j
                        })
                    else:
                        results.append(result)

                # Rate limiting delay between batches
                if i + batch_size < len(prompts):
                    await asyncio.sleep(2)  # 2 second delay between batches

            except Exception as e:
                logger.error(f"Batch processing failed: {e}")
                # Add error results for the entire batch
                for j in range(len(batch_prompts)):
                    results.append({
                        "success": False,
                        "error": f"Batch processing error: {str(e)}",
                        "prompt_index": i + j
                    })

        logger.info(f"Batch generation completed: {len(results)} images processed")
        return results

    def validate_image_quality(self, image_path: str) -> Dict[str, Any]:
        """Validate generated image quality.

        Args:
            image_path: Path to image file

        Returns:
            Quality validation result
        """
        result = {
            "valid": True,
            "issues": [],
            "metrics": {},
            "recommendations": []
        }

        try:
            image = Image.open(image_path)

            # Basic metrics
            width, height = image.size
            result["metrics"].update({
                "width": width,
                "height": height,
                "aspect_ratio": width / height,
                "total_pixels": width * height,
                "format": image.format,
                "mode": image.mode
            })

            # Size validation
            if width < 512 or height < 512:
                result["issues"].append("Image resolution is quite low")
                result["recommendations"].append("Consider using higher resolution settings")

            # Aspect ratio validation
            aspect_ratio = width / height
            if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                result["issues"].append("Unusual aspect ratio detected")

            # File size validation
            file_size = Path(image_path).stat().st_size
            result["metrics"]["file_size_mb"] = file_size / (1024 * 1024)

            if file_size < 100 * 1024:  # Less than 100KB
                result["issues"].append("File size is very small, may indicate low quality")
            elif file_size > 50 * 1024 * 1024:  # More than 50MB
                result["issues"].append("File size is very large")
                result["recommendations"].append("Consider compression for web use")

            # Set overall validity
            if len(result["issues"]) > 3:
                result["valid"] = False
                result["recommendations"].append("Consider regenerating with different parameters")

        except Exception as e:
            result["valid"] = False
            result["issues"].append(f"Failed to analyze image: {str(e)}")

        return result
