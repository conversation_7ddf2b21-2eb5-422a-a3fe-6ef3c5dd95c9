"""
Validation Utilities

Provides validation functions for story prompts, image parameters, and effects configuration.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union
import jsonschema

logger = logging.getLogger(__name__)


# Schema definitions
STORY_PROMPT_SCHEMA = {
    "type": "object",
    "properties": {
        "title": {"type": "string", "minLength": 1, "maxLength": 200},
        "genre": {"type": "string", "enum": ["fantasy", "sci-fi", "mystery", "romance", "adventure", "horror", "drama"]},
        "setting": {"type": "string", "minLength": 1, "maxLength": 500},
        "main_characters": {
            "type": "array",
            "items": {"type": "string"},
            "minItems": 1,
            "maxItems": 10
        },
        "plot_summary": {"type": "string", "minLength": 50, "maxLength": 2000},
        "mood": {"type": "string", "minLength": 1, "maxLength": 100},
        "target_image_style": {"type": "string", "minLength": 1, "maxLength": 200}
    },
    "required": ["title", "plot_summary"],
    "additionalProperties": True
}

IMAGE_PARAMS_SCHEMA = {
    "type": "object",
    "properties": {
        "prompt": {"type": "string", "minLength": 10, "maxLength": 4000},
        "size": {"type": "string", "enum": ["1024x1024", "1792x1024", "1024x1792", "512x512", "768x768"]},
        "quality": {"type": "string", "enum": ["standard", "hd"]},
        "style": {"type": "string", "enum": ["vivid", "natural"]},
        "model": {"type": "string"},
        "steps": {"type": "integer", "minimum": 1, "maximum": 150},
        "cfg_scale": {"type": "number", "minimum": 1.0, "maximum": 20.0}
    },
    "required": ["prompt"],
    "additionalProperties": True
}

EFFECTS_CONFIG_SCHEMA = {
    "type": "object",
    "properties": {
        "intensity": {"type": "string", "enum": ["low", "medium", "high"]},
        "effects": {
            "type": "array",
            "items": {"type": "string"},
            "uniqueItems": True
        },
        "output_format": {"type": "string", "enum": ["png", "jpg", "gif", "mp4"]},
        "duration": {"type": "number", "minimum": 1, "maximum": 60},
        "fps": {"type": "integer", "minimum": 1, "maximum": 60}
    },
    "additionalProperties": True
}


def validate_story_prompt(story_data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate story prompt data.
    
    Args:
        story_data: Story prompt data to validate
        
    Returns:
        Validation result with success status and errors
    """
    result = {"valid": True, "errors": [], "warnings": []}
    
    try:
        # Schema validation
        jsonschema.validate(story_data, STORY_PROMPT_SCHEMA)
        
        # Additional content validation
        if "plot_summary" in story_data:
            plot_summary = story_data["plot_summary"]
            
            # Check for inappropriate content (basic filtering)
            inappropriate_keywords = [
                "violence", "explicit", "graphic", "disturbing", 
                "inappropriate", "offensive", "harmful"
            ]
            
            for keyword in inappropriate_keywords:
                if keyword.lower() in plot_summary.lower():
                    result["warnings"].append(f"Potentially inappropriate content detected: {keyword}")
            
            # Check for visual description quality
            visual_keywords = [
                "see", "look", "appear", "visual", "color", "light", 
                "shadow", "bright", "dark", "beautiful", "stunning"
            ]
            
            visual_score = sum(1 for keyword in visual_keywords if keyword in plot_summary.lower())
            if visual_score < 2:
                result["warnings"].append("Story may benefit from more visual descriptions")
        
        # Validate character count
        if "main_characters" in story_data:
            characters = story_data["main_characters"]
            if len(characters) > 5:
                result["warnings"].append("Many characters may make image generation complex")
        
        logger.info("Story prompt validation completed successfully")
        
    except jsonschema.ValidationError as e:
        result["valid"] = False
        result["errors"].append(f"Schema validation error: {e.message}")
        logger.error(f"Story prompt validation failed: {e.message}")
    
    except Exception as e:
        result["valid"] = False
        result["errors"].append(f"Validation error: {str(e)}")
        logger.error(f"Story prompt validation error: {e}")
    
    return result


def validate_image_params(image_params: Dict[str, Any]) -> Dict[str, Any]:
    """Validate image generation parameters.
    
    Args:
        image_params: Image parameters to validate
        
    Returns:
        Validation result with success status and errors
    """
    result = {"valid": True, "errors": [], "warnings": []}
    
    try:
        # Schema validation
        jsonschema.validate(image_params, IMAGE_PARAMS_SCHEMA)
        
        # Additional validation
        prompt = image_params.get("prompt", "")
        
        # Check prompt length and quality
        if len(prompt) < 20:
            result["warnings"].append("Short prompts may produce less detailed images")
        
        if len(prompt) > 2000:
            result["warnings"].append("Very long prompts may be truncated by some APIs")
        
        # Check for style consistency
        style_keywords = ["photorealistic", "artistic", "cartoon", "anime", "painting"]
        style_count = sum(1 for keyword in style_keywords if keyword.lower() in prompt.lower())
        
        if style_count > 2:
            result["warnings"].append("Multiple conflicting styles detected in prompt")
        
        # Validate size compatibility
        size = image_params.get("size", "1024x1024")
        if size in ["1792x1024", "1024x1792"] and image_params.get("model") == "dall-e-2":
            result["errors"].append("DALL-E 2 doesn't support rectangular aspect ratios")
            result["valid"] = False
        
        # Check quality and model compatibility
        quality = image_params.get("quality", "standard")
        if quality == "hd" and image_params.get("model") == "dall-e-2":
            result["warnings"].append("HD quality not available for DALL-E 2")
        
        logger.info("Image parameters validation completed successfully")
        
    except jsonschema.ValidationError as e:
        result["valid"] = False
        result["errors"].append(f"Schema validation error: {e.message}")
        logger.error(f"Image parameters validation failed: {e.message}")
    
    except Exception as e:
        result["valid"] = False
        result["errors"].append(f"Validation error: {str(e)}")
        logger.error(f"Image parameters validation error: {e}")
    
    return result


def validate_effects_config(effects_config: Dict[str, Any]) -> Dict[str, Any]:
    """Validate effects processing configuration.
    
    Args:
        effects_config: Effects configuration to validate
        
    Returns:
        Validation result with success status and errors
    """
    result = {"valid": True, "errors": [], "warnings": []}
    
    try:
        # Schema validation
        jsonschema.validate(effects_config, EFFECTS_CONFIG_SCHEMA)
        
        # Additional validation
        output_format = effects_config.get("output_format", "png")
        duration = effects_config.get("duration", 0)
        
        # Validate format and duration compatibility
        if output_format in ["gif", "mp4"] and duration <= 0:
            result["errors"].append("Animated formats require duration > 0")
            result["valid"] = False
        
        if output_format in ["png", "jpg"] and duration > 0:
            result["warnings"].append("Duration ignored for static image formats")
        
        # Check effects compatibility
        effects = effects_config.get("effects", [])
        motion_effects = ["ken_burns", "zoom", "pan", "rotate"]
        static_effects = ["contrast", "saturation", "blur", "sharpen"]
        
        has_motion = any(effect in motion_effects for effect in effects)
        has_static = any(effect in static_effects for effect in effects)
        
        if has_motion and output_format in ["png", "jpg"]:
            result["warnings"].append("Motion effects ignored for static formats")
        
        # Validate FPS for video formats
        fps = effects_config.get("fps", 24)
        if output_format == "mp4" and fps > 30:
            result["warnings"].append("High FPS may increase file size significantly")
        
        if output_format == "gif" and fps > 15:
            result["warnings"].append("High FPS GIFs may be very large")
        
        logger.info("Effects configuration validation completed successfully")
        
    except jsonschema.ValidationError as e:
        result["valid"] = False
        result["errors"].append(f"Schema validation error: {e.message}")
        logger.error(f"Effects configuration validation failed: {e.message}")
    
    except Exception as e:
        result["valid"] = False
        result["errors"].append(f"Validation error: {str(e)}")
        logger.error(f"Effects configuration validation error: {e}")
    
    return result


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for cross-platform compatibility.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    invalid_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    
    # Limit length
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    
    # Ensure not empty
    if not sanitized:
        sanitized = "untitled"
    
    return sanitized


def validate_api_key(api_key: str, service: str) -> bool:
    """Validate API key format.
    
    Args:
        api_key: API key to validate
        service: Service name (openai, stability, huggingface)
        
    Returns:
        True if format appears valid
    """
    if not api_key or not isinstance(api_key, str):
        return False
    
    # Basic format validation
    if service == "openai":
        return api_key.startswith("sk-") and len(api_key) > 20
    elif service == "stability":
        return len(api_key) > 10  # Basic length check
    elif service == "huggingface":
        return api_key.startswith("hf_") and len(api_key) > 10
    
    return len(api_key) > 5  # Generic minimum length


def validate_content_safety(text: str) -> Dict[str, Any]:
    """Basic content safety validation.
    
    Args:
        text: Text content to validate
        
    Returns:
        Safety validation result
    """
    result = {"safe": True, "issues": [], "confidence": 1.0}
    
    # Basic keyword filtering
    unsafe_keywords = [
        "violence", "weapon", "harm", "illegal", "explicit", 
        "inappropriate", "offensive", "disturbing", "graphic"
    ]
    
    text_lower = text.lower()
    detected_issues = []
    
    for keyword in unsafe_keywords:
        if keyword in text_lower:
            detected_issues.append(keyword)
    
    if detected_issues:
        result["safe"] = False
        result["issues"] = detected_issues
        result["confidence"] = max(0.1, 1.0 - len(detected_issues) * 0.2)
    
    return result
