"""
Storytelling Pipeline Core Module

This module contains the core components for automated storytelling and visual content generation.
"""

from .story_generator import StoryPromptGenerator
from .image_generator import ImageGenerator
from .effects_processor import EffectsProcessor
from .pipeline import StorytellingPipeline

__version__ = "1.0.0"
__author__ = "Buddha Project"

__all__ = [
    "StoryPromptGenerator",
    "ImageGenerator", 
    "EffectsProcessor",
    "StorytellingPipeline"
]
