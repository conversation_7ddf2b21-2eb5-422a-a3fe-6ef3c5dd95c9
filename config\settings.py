"""
Configuration Management System

Handles loading and validation of configuration from YAML files and environment variables.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and validation."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. Defaults to config.yaml
        """
        self.config_path = config_path or "config.yaml"
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file with environment variable overrides."""
        config = {}
        
        # Load from YAML file if it exists
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {self.config_path}")
            except Exception as e:
                logger.warning(f"Failed to load config file {self.config_path}: {e}")
        else:
            logger.info(f"Config file {self.config_path} not found, using defaults")
        
        # Apply environment variable overrides
        config = self._apply_env_overrides(config)
        
        return config
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        # API Keys
        if os.getenv('OPENAI_API_KEY'):
            config.setdefault('api_keys', {})['openai'] = os.getenv('OPENAI_API_KEY')
        
        if os.getenv('STABILITY_API_KEY'):
            config.setdefault('api_keys', {})['stability'] = os.getenv('STABILITY_API_KEY')
        
        if os.getenv('HUGGINGFACE_API_KEY'):
            config.setdefault('api_keys', {})['huggingface'] = os.getenv('HUGGINGFACE_API_KEY')
        
        # Pipeline settings
        if os.getenv('DEFAULT_OUTPUT_DIR'):
            config.setdefault('pipeline', {}).setdefault('output_structure', {})['base_dir'] = os.getenv('DEFAULT_OUTPUT_DIR')
        
        if os.getenv('LOG_LEVEL'):
            config.setdefault('pipeline', {}).setdefault('logging', {})['level'] = os.getenv('LOG_LEVEL')
        
        # Image generation settings
        if os.getenv('DEFAULT_IMAGE_SIZE'):
            size = os.getenv('DEFAULT_IMAGE_SIZE')
            config.setdefault('image_generation', {}).setdefault('openai', {})['size'] = size
        
        return config
    
    def _validate_config(self):
        """Validate configuration structure and required fields."""
        required_sections = ['story_generation', 'image_generation', 'effects_processing', 'pipeline']
        
        for section in required_sections:
            if section not in self.config:
                logger.warning(f"Missing configuration section: {section}")
                self.config[section] = {}
        
        # Validate API keys are available
        api_keys = self.get_api_keys()
        if not api_keys.get('openai'):
            logger.warning("OpenAI API key not configured")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to configuration value (e.g., 'story_generation.openai.model')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_api_keys(self) -> Dict[str, str]:
        """Get all configured API keys."""
        return self.config.get('api_keys', {})
    
    def get_story_config(self) -> Dict[str, Any]:
        """Get story generation configuration."""
        return self.config.get('story_generation', {})
    
    def get_image_config(self) -> Dict[str, Any]:
        """Get image generation configuration."""
        return self.config.get('image_generation', {})
    
    def get_effects_config(self) -> Dict[str, Any]:
        """Get effects processing configuration."""
        return self.config.get('effects_processing', {})
    
    def get_pipeline_config(self) -> Dict[str, Any]:
        """Get pipeline configuration."""
        return self.config.get('pipeline', {})


# Global configuration instance
_config_manager = None


def load_config(config_path: Optional[str] = None) -> ConfigManager:
    """Load and return global configuration manager."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager


def get_api_keys() -> Dict[str, str]:
    """Get API keys from configuration."""
    config = load_config()
    return config.get_api_keys()


def setup_logging(config: Optional[ConfigManager] = None):
    """Setup logging based on configuration."""
    if config is None:
        config = load_config()
    
    log_config = config.get_pipeline_config().get('logging', {})
    
    level = getattr(logging, log_config.get('level', 'INFO').upper())
    format_str = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Configure root logger
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_config.get('file', 'storytelling_pipeline.log'))
        ]
    )
    
    logger.info("Logging configured successfully")
