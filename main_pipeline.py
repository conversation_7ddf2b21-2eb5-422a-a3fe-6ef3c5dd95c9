#!/usr/bin/env python3
"""
Main CLI Entry Point for Storytelling Pipeline

Comprehensive command-line interface for the automated storytelling and visual content generation pipeline.
"""

import asyncio
import click
import json
import sys
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from core.pipeline import StorytellingPipeline
from config.settings import setup_logging

console = Console()


class ProgressTracker:
    """Progress tracking for CLI display."""
    
    def __init__(self):
        self.progress = None
        self.task_id = None
    
    def start(self, total_steps: int, description: str = "Processing"):
        """Start progress tracking."""
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        )
        self.progress.start()
        self.task_id = self.progress.add_task(description, total=total_steps)
    
    def update(self, completed: int, total: int, current_task: str):
        """Update progress."""
        if self.progress and self.task_id:
            self.progress.update(
                self.task_id, 
                completed=completed, 
                total=total,
                description=current_task
            )
    
    def stop(self):
        """Stop progress tracking."""
        if self.progress:
            self.progress.stop()


@click.group()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """Automated Storytelling and Visual Content Generation Pipeline"""
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose
    
    # Setup logging
    if verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)


@cli.command()
@click.option('--method', '-m', default='auto', 
              type=click.Choice(['openai', 'template', 'interactive', 'random', 'buddha', 'auto']),
              help='Story generation method')
@click.option('--prompt', '-p', help='Custom story prompt')
@click.option('--image-service', '-i', default='auto',
              type=click.Choice(['openai', 'stability', 'auto']),
              help='Image generation service')
@click.option('--effects', '-e', multiple=True, help='Visual effects to apply')
@click.option('--format', '-f', default='png',
              type=click.Choice(['png', 'jpg', 'gif', 'mp4']),
              help='Output format')
@click.option('--intensity', default='medium',
              type=click.Choice(['low', 'medium', 'high']),
              help='Effect intensity')
@click.pass_context
def generate(ctx, method, prompt, image_service, effects, format, intensity):
    """Generate a complete story with image and effects"""
    
    try:
        # Initialize pipeline
        pipeline = StorytellingPipeline(ctx.obj['config'])
        
        # Setup progress tracking
        progress_tracker = ProgressTracker()
        pipeline.set_progress_callback(progress_tracker.update)
        
        # Validate setup
        validation = pipeline.validate_pipeline_setup()
        if not validation['valid']:
            console.print("[red]Pipeline setup validation failed:[/red]")
            for issue in validation['issues']:
                console.print(f"  ❌ {issue}")
            return
        
        if validation['warnings']:
            console.print("[yellow]Warnings:[/yellow]")
            for warning in validation['warnings']:
                console.print(f"  ⚠️  {warning}")
        
        # Start generation
        console.print(f"\n🎭 Starting story generation with method: [bold]{method}[/bold]")
        
        progress_tracker.start(4, "Initializing story generation")
        
        # Run generation
        result = asyncio.run(pipeline.generate_complete_story(
            story_method=method,
            story_prompt=prompt,
            image_service=image_service,
            effects=list(effects) if effects else None,
            output_format=format,
            intensity=intensity
        ))
        
        progress_tracker.stop()
        
        # Display results
        if result['success']:
            console.print("\n✅ [green]Story generation completed successfully![/green]")
            
            # Create results table
            table = Table(title="Generation Results")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Details")
            
            story_title = result['story_data'].get('title', 'Untitled')
            table.add_row("Story", "✅ Success", f"Title: {story_title}")
            
            if result['image_data']:
                image_path = Path(result['image_data']['saved_path']).name
                table.add_row("Image", "✅ Success", f"File: {image_path}")
            
            if result['effects_data']:
                effects_applied = ', '.join(result['effects_data'].get('effects_applied', []))
                table.add_row("Effects", "✅ Success", f"Applied: {effects_applied}")
            
            final_output = Path(result['final_output']).name if result['final_output'] else 'N/A'
            table.add_row("Final Output", "✅ Success", f"File: {final_output}")
            
            console.print(table)
            
            # Show session info
            console.print(f"\n📁 Session directory: [blue]{result['session_directory']}[/blue]")
            console.print(f"📄 Summary file: [blue]{result.get('summary_file', 'N/A')}[/blue]")
            
        else:
            console.print("\n❌ [red]Story generation failed![/red]")
            for error in result.get('errors', []):
                console.print(f"  🔥 {error}")
    
    except Exception as e:
        console.print(f"\n💥 [red]Unexpected error: {str(e)}[/red]")
        if ctx.obj['verbose']:
            import traceback
            console.print(traceback.format_exc())


@cli.command()
@click.option('--count', '-n', default=3, help='Number of stories to generate')
@click.option('--method', '-m', default='random',
              type=click.Choice(['openai', 'template', 'random', 'buddha']),
              help='Story generation method')
@click.option('--format', '-f', default='png',
              type=click.Choice(['png', 'jpg', 'gif', 'mp4']),
              help='Output format')
@click.pass_context
def batch(ctx, count, method, format):
    """Generate multiple stories in batch"""
    
    try:
        pipeline = StorytellingPipeline(ctx.obj['config'])
        
        # Setup progress tracking
        progress_tracker = ProgressTracker()
        pipeline.set_progress_callback(progress_tracker.update)
        
        console.print(f"\n🎭 Starting batch generation of [bold]{count}[/bold] stories")
        
        progress_tracker.start(count * 4, "Starting batch generation")
        
        # Run batch generation
        result = asyncio.run(pipeline.generate_batch_stories(
            count=count,
            story_method=method,
            output_format=format
        ))
        
        progress_tracker.stop()
        
        # Display results
        if result['success']:
            console.print(f"\n✅ [green]Batch generation completed![/green]")
            console.print(f"📊 Success rate: {result['successful_generations']}/{result['total_requested']} ({result['successful_generations']/result['total_requested']*100:.1f}%)")
        else:
            console.print(f"\n⚠️  [yellow]Batch generation completed with errors[/yellow]")
            console.print(f"📊 Success rate: {result['successful_generations']}/{result['total_requested']} ({result['successful_generations']/result['total_requested']*100:.1f}%)")
        
        console.print(f"📁 Session directory: [blue]{result['session_directory']}[/blue]")
        
    except Exception as e:
        console.print(f"\n💥 [red]Batch generation failed: {str(e)}[/red]")


@cli.command()
@click.option('--count', '-n', default=5, help='Number of stories for video sequence')
@click.option('--duration', '-d', default=8.0, help='Duration per scene in seconds')
@click.option('--audio', '-a', help='Background audio file path')
@click.pass_context
def video(ctx, count, duration, audio):
    """Create video sequence from multiple stories"""
    
    try:
        pipeline = StorytellingPipeline(ctx.obj['config'])
        
        # Setup progress tracking
        progress_tracker = ProgressTracker()
        pipeline.set_progress_callback(progress_tracker.update)
        
        console.print(f"\n🎬 Creating video sequence with [bold]{count}[/bold] stories")
        
        progress_tracker.start(count * 3 + 2, "Starting video sequence creation")
        
        # Run video creation
        result = asyncio.run(pipeline.create_story_video_sequence(
            story_count=count,
            duration_per_scene=duration,
            audio_path=audio
        ))
        
        progress_tracker.stop()
        
        # Display results
        if result['success']:
            console.print(f"\n🎥 [green]Video sequence created successfully![/green]")
            console.print(f"📹 Video file: [blue]{result['video_path']}[/blue]")
            console.print(f"⏱️  Duration: {result.get('video_duration', 'Unknown')} seconds")
            console.print(f"🔊 Audio: {'Yes' if result.get('has_audio') else 'No'}")
        else:
            console.print(f"\n❌ [red]Video sequence creation failed![/red]")
            for error in result.get('errors', []):
                console.print(f"  🔥 {error}")
        
    except Exception as e:
        console.print(f"\n💥 [red]Video creation failed: {str(e)}[/red]")


@cli.command()
@click.pass_context
def status(ctx):
    """Show pipeline status and configuration"""
    
    try:
        pipeline = StorytellingPipeline(ctx.obj['config'])
        status_info = pipeline.get_pipeline_status()
        
        # Create status panel
        status_text = f"""
[bold]Pipeline Status:[/bold] ✅ Initialized
[bold]Current Session:[/bold] {status_info['current_session'] or 'None'}
[bold]Progress:[/bold] {status_info['progress']['completed_steps']}/{status_info['progress']['total_steps']} ({status_info['progress']['percentage']:.1f}%)

[bold]Components:[/bold]
  📝 Story Generator: {len(status_info['components']['story_generator']['available_methods'])} methods available
  🎨 Image Generator: {len(status_info['components']['image_generator']['available_services'])} services available  
  ✨ Effects Processor: {len(status_info['components']['effects_processor']['available_effects']['color_effects']) + len(status_info['components']['effects_processor']['available_effects']['artistic_effects'])} effects available

[bold]Configuration:[/bold]
  📁 Output Directory: {status_info['configuration']['output_directory']}
  🔑 API Keys Configured: {status_info['configuration']['api_keys_configured']}
        """
        
        console.print(Panel(status_text.strip(), title="🤖 Storytelling Pipeline Status", border_style="blue"))
        
        # Validation
        validation = pipeline.validate_pipeline_setup()
        if validation['warnings'] or not validation['valid']:
            console.print("\n⚠️  [yellow]Setup Issues:[/yellow]")
            for warning in validation['warnings']:
                console.print(f"  ⚠️  {warning}")
            for issue in validation['issues']:
                console.print(f"  ❌ {issue}")
        
        if validation['recommendations']:
            console.print("\n💡 [blue]Recommendations:[/blue]")
            for rec in validation['recommendations']:
                console.print(f"  💡 {rec}")
    
    except Exception as e:
        console.print(f"\n💥 [red]Status check failed: {str(e)}[/red]")


@cli.command()
@click.option('--session', '-s', help='Session directory path')
@click.option('--format', '-f', default='json', 
              type=click.Choice(['json', 'zip']),
              help='Export format')
@click.pass_context
def export(ctx, session, format):
    """Export session data"""
    
    try:
        pipeline = StorytellingPipeline(ctx.obj['config'])
        
        export_path = pipeline.export_session_data(
            session_path=session,
            export_format=format
        )
        
        console.print(f"✅ [green]Session exported successfully![/green]")
        console.print(f"📦 Export file: [blue]{export_path}[/blue]")
        
    except Exception as e:
        console.print(f"💥 [red]Export failed: {str(e)}[/red]")


if __name__ == '__main__':
    cli()
