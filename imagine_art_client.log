2025-07-24 10:11:15,121 - __main__ - ERROR - Error in example usage: 19 validation errors for ImagineArtConfig
openai_api_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='your_openai_api_key_here', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
openai_org_id
  Extra inputs are not permitted [type=extra_forbidden, input_value='your_organization_id_here', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
stability_api_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='your_stability_ai_api_key_here', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
huggingface_api_key
  Extra inputs are not permitted [type=extra_forbidden, input_value='your_huggingface_api_key_here', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
default_output_dir
  Extra inputs are not permitted [type=extra_forbidden, input_value='output', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
default_image_size
  Extra inputs are not permitted [type=extra_forbidden, input_value='1024x1024', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
default_video_duration
  Extra inputs are not permitted [type=extra_forbidden, input_value='8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
default_effects_intensity
  Extra inputs are not permitted [type=extra_forbidden, input_value='medium', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
log_level
  Extra inputs are not permitted [type=extra_forbidden, input_value='INFO', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
log_file
  Extra inputs are not permitted [type=extra_forbidden, input_value='storytelling_pipeline.log', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
api_rate_limit_requests_per_minute
  Extra inputs are not permitted [type=extra_forbidden, input_value='60', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
api_retry_attempts
  Extra inputs are not permitted [type=extra_forbidden, input_value='3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
api_retry_delay
  Extra inputs are not permitted [type=extra_forbidden, input_value='2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
image_quality
  Extra inputs are not permitted [type=extra_forbidden, input_value='high', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
video_fps
  Extra inputs are not permitted [type=extra_forbidden, input_value='24', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
audio_bitrate
  Extra inputs are not permitted [type=extra_forbidden, input_value='128k', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
enable_cache
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
cache_duration_hours
  Extra inputs are not permitted [type=extra_forbidden, input_value='24', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
cache_dir
  Extra inputs are not permitted [type=extra_forbidden, input_value='.cache', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-24 10:11:44,706 - __main__ - WARNING - Attempt 1 failed: 404 Client Error: Not Found for url: https://api.imagine.art/v1/generate
2025-07-24 10:11:50,043 - __main__ - WARNING - Attempt 2 failed: 404 Client Error: Not Found for url: https://api.imagine.art/v1/generate
2025-07-24 10:11:55,379 - __main__ - WARNING - Attempt 3 failed: 404 Client Error: Not Found for url: https://api.imagine.art/v1/generate
2025-07-24 10:11:55,380 - __main__ - ERROR - Failed after 3 attempts: 404 Client Error: Not Found for url: https://api.imagine.art/v1/generate
